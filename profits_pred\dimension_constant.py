from src.utils.resource_loader import LOGGER as log
import pandas as pd

dimension_subcategory_dim = [
    'scenarios', 'bg_code',
    'bg_name', 'oversea_code',
    'oversea_desc', 'lv1_code', 'lv1_name',
    'lv2_code', 'lv2_name',
    'currency', 'dimension_subcategory_code',
    'dimension_subcategory_cn_name',
    'dimension_subcategory_en_name',
]
dimension_group_dim = [
    'scenarios', 'bg_code',
    'bg_name', 'oversea_code', 'oversea_desc',
    'lv1_code', 'lv1_name', 'lv2_code', 'lv2_name',
    'currency', 'dimension_group_code', 'dimension_group_cn_name', 'dimension_group_en_name'
]
lv2_dim = [
    'scenarios', 'bg_code', 'bg_name',
    'oversea_code', 'oversea_desc',
    'lv1_code',
    'lv1_name',
    'lv2_code',
    'lv2_name'
]

# 调用api预测传参常量
# sit
# problemId = 585942
# taskId = 585942
# unit_cost_solutionId = 11306
# unit_price_solutionId = 11308
# other_param_solutionId = 11310

#uat
problemId = 11050
taskId = 11050
unit_cost_solutionId = 11216
unit_price_solutionId = 11215
other_param_solutionId = 11214


def check_input(df: pd.DataFrame, cols_to_check: list):
    null_stats = pd.DataFrame({
        '空值数量': df[cols_to_check].isna().sum(),
        '空值占比': df[cols_to_check].isna().mean().round(4) * 100  # 转换为百分比
    })
    if not null_stats.empty:
        log.info(
            f"来源:dm_fop_dimension_lv2_tgt_period_filled_t表,有空值，{null_stats.to_string()}"
        )