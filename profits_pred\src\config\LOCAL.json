{"DEPLOY": {"cloud": "HIS", "infra": "LOCAL", "enable_doc": true, "service_name": "/mmae-pyxis", "uri_prefix": "/mmae-pyxis", "config_server": {"url": "http://appconfig-beta.huawei.com/ConfigCenter/services/saasConfigcenterGetConfig", "app_id": "com.huawei.fsppub", "du": "opt_fsp_pub_service", "environment": "kwe_sit", "region": "kwe", "version": "1.0", "config_parts": ["ed5e1ae6242b4fa5b2b4537ed7899e1f", "f263811e71da4e7fad2a8c40057a66a4"]}, "api_gateway": {"authorization": {"url": "http://kwe-beta.huawei.com/ApiCommonQuery/appToken/getRestAppDynamicToken"}, "authentication": {"sgov_token": "http://kwe-beta.huawei.com/ApiCommonQuery/appToken/validateToken", "sgov_uri": "http://kwe-beta.huawei.com/ApiCommonQuery/appToken/authorizeByURI"}}}, "LOGGER": {"name": "<PERSON><PERSON>", "output_directory": null, "log_to_file": false, "log_format": "%(asctime)s | PID-%(process)d-%(threadName)s-%(thread)d | %(name)s | %(filename)s:%(lineno)d | %(levelname)s | %(message)s", "level": "DEBUG", "max_file_size": 102400000, "backup_count": 5}, "RDB": {"pg_demo": {"datasource_name": "fin_dm_opt_tod", "tables": ["databasechangelog"], "echo": true}}, "OBS": {"demo_bucket": {"bucket": "fai-mlops-demo", "pool_size": 5, "max_overflow": 5, "pool_timeout": 2, "pool_recycle": 3600, "pool_pre_ping": true}}, "LIMITER": {"storage_uri": null, "strategy": "moving-window", "unit": "minute", "frequency": 10}, "API": {"upload_url": "https://opt.hissit.huawei.com/opt/pc/ifs/v1/version/createVersionStartForecastByFile", "forecast_result_url": "https://opt.hissit.huawei.com/opt/pc/ifs/v1/version/generateTemporaryURL", "Referer": "http://opt.hissit.huawei.com"}}