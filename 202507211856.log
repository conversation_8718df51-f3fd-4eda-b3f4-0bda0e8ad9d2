)
2025-07-21 18:29:22 log.py [line:130] ERROR: Exception during reset or similar
Traceback (most recent call last):
File "/usr/local/lib/python3.9/site-packages/sqlalchemy/pool/base.py", line 682, in _finalize_fairy
fairy._reset(pool)
File "/usr/local/lib/python3.9/site-packages/sqlalchemy/pool/base.py", line 887, in _reset
pool._dialect.do_rollback(self)
File "/usr/local/lib/python3.9/site-packages/sqlalchemy/engine/default.py", line 667, in do_rollback
dbapi_connection.rollback()
psycopg2.OperationalError: SSL SYSCALL error: EOF detected

2025-07-21 18:29:22 log.py [line:117] INFO: Invalidate connection <connection object at 0x7f4320bcd630; dsn: 'user=fin_dm_opt_fop password=xxx dbname=fin_opt_uat_d1 host=dws-fin-opt-test-cls-dws.beta.hic.cloud port=8000 sslmode=require', closed: 2> (reason: OperationalError:SSL SYSCALL error: EOF detected
)
2025-07-21 18:29:22 log.py [line:130] ERROR: Exception during reset or similar
Traceback (most recent call last):
File "/usr/local/lib/python3.9/site-packages/sqlalchemy/pool/base.py", line 682, in _finalize_fairy
fairy._reset(pool)
File "/usr/local/lib/python3.9/site-packages/sqlalchemy/pool/base.py", line 887, in _reset
pool._dialect.do_rollback(self)
File "/usr/local/lib/python3.9/site-packages/sqlalchemy/engine/default.py", line 667, in do_rollback
dbapi_connection.rollback()
psycopg2.OperationalError: SSL SYSCALL error: EOF detected

2025-07-21 18:29:22 log.py [line:117] INFO: Invalidate connection <connection object at 0x7f42daf5a040; dsn: 'user=fin_dm_opt_fop password=xxx dbname=fin_opt_uat_d1 host=dws-fin-opt-test-cls-dws.beta.hic.cloud port=8000 sslmode=require', closed: 2> (reason: OperationalError:SSL SYSCALL error: EOF detected
)
2025-07-21 18:29:22 log.py [line:130] ERROR: Exception during reset or similar
Traceback (most recent call last):
File "/usr/local/lib/python3.9/site-packages/sqlalchemy/pool/base.py", line 682, in _finalize_fairy
fairy._reset(pool)
File "/usr/local/lib/python3.9/site-packages/sqlalchemy/pool/base.py", line 887, in _reset
pool._dialect.do_rollback(self)
File "/usr/local/lib/python3.9/site-packages/sqlalchemy/engine/default.py", line 667, in do_rollback
dbapi_connection.rollback()
psycopg2.OperationalError: SSL SYSCALL error: EOF detected

2025-07-21 18:29:22 log.py [line:117] INFO: Invalidate connection <connection object at 0x7f4320bcb040; dsn: 'user=fin_dm_opt_fop password=xxx dbname=fin_opt_uat_d1 host=dws-fin-opt-test-cls-dws.beta.hic.cloud port=8000 sslmode=require', closed: 2> (reason: OperationalError:SSL SYSCALL error: EOF detected
)
2025-07-21 18:29:22 log.py [line:130] ERROR: Exception during reset or similar
Traceback (most recent call last):
File "/usr/local/lib/python3.9/site-packages/sqlalchemy/pool/base.py", line 682, in _finalize_fairy
fairy._reset(pool)
File "/usr/local/lib/python3.9/site-packages/sqlalchemy/pool/base.py", line 887, in _reset
pool._dialect.do_rollback(self)
File "/usr/local/lib/python3.9/site-packages/sqlalchemy/engine/default.py", line 667, in do_rollback
dbapi_connection.rollback()
psycopg2.OperationalError: SSL SYSCALL error: EOF detected

2025-07-21 18:29:22 log.py [line:117] INFO: Invalidate connection <connection object at 0x7f42daf153d0; dsn: 'user=fin_dm_opt_fop password=xxx dbname=fin_opt_uat_d1 host=dws-fin-opt-test-cls-dws.beta.hic.cloud port=8000 sslmode=require', closed: 2> (reason: OperationalError:SSL SYSCALL error: EOF detected
)
2025-07-21 18:29:30,786 INFO aipaas_engine [api_client.py-notice-46] [p-137 t-139764304869184] (instance_id:255e6ef2-86e7-42c3-a456-6f05a56a9554 aipaas_uri:aipaas_uri ip:************ trace_id:c1f74606) report notice: step5：service down and restart. error message: user process exited, None File "/usr/local/lib/python3.9/site-packages/sqlalchemy/engine/default.py", line 667, in do_rollback
dbapi_connection.rollback()
psycopg2.OperationalError: SSL SYSCALL error: EOF detected

2025-07-21 18:29:22 log.py [line:117] INFO: Invalidate connection <connection object at 0x7f42daf5a040; dsn: 'user=fin_dm_opt_fop password=xxx dbname=fin_opt_uat_d1 host=dws-fin-opt-test-cls-dws.beta.hic.cloud port=8000 sslmode=require', closed: 2> (reason: OperationalError:SSL SYSCALL error: EOF detected
)
2025-07-21 18:29:22 log.py [line:130] ERROR: Exception during reset or similar
Traceback (most recent call last):
File "/usr/local/lib/python3.9/site-packages/sqlalchemy/pool/base.py", line 682, in _finalize_fairy
fairy._reset(pool)
File "/usr/local/lib/python3.9/site-packages/sqlalchemy/pool/base.py", line 887, in _reset
pool._dialect.do_rollback(self)
File "/usr/local/lib/python3.9/site-packages/sqlalchemy/engine/default.py", line 667, in do_rollback
dbapi_connection.rollback()
psycopg2.OperationalError: SSL SYSCALL error: EOF detected

2025-07-21 18:29:22 log.py [line:117] INFO: Invalidate connection <connection object at 0x7f4320bcb040; dsn: 'user=fin_dm_opt_fop password=xxx dbname=fin_opt_uat_d1 host=dws-fin-opt-test-cls-dws.beta.hic.cloud port=8000 sslmode=require', closed: 2> (reason: OperationalError:SSL SYSCALL error: EOF detected
)
2025-07-21 18:29:22 log.py [line:130] ERROR: Exception during reset or similar
Traceback (most recent call last):
File "/usr/local/lib/python3.9/site-packages/sqlalchemy/pool/base.py", line 682, in _finalize_fairy
fairy._reset(pool)
File "/usr/local/lib/python3.9/site-packages/sqlalchemy/pool/base.py", line 887, in _reset
pool._dialect.do_rollback(self)
File "/usr/local/lib/python3.9/site-packages/sqlalchemy/engine/default.py", line 667, in do_rollback
dbapi_connection.rollback()
psycopg2.OperationalError: SSL SYSCALL error: EOF detected

2025-07-21 18:29:22 log.py [line:117] INFO: Invalidate connection <connection object at 0x7f42daf153d0; dsn: 'user=fin_dm_opt_fop password=xxx dbname=fin_opt_uat_d1 host=dws-fin-opt-test-cls-dws.beta.hic.cloud port=8000 sslmode=require', closed: 2> (reason: OperationalError:SSL SYSCALL error: EOF detected
).,error info:业务服务挂掉重启,error code:1405.
/usr/local/lib/python3.9/site-packages/urllib3/connectionpool.py:1043: InsecureRequestWarning: Unverified HTTPS request is being made to host 'iam.his-op-beta.huawei.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/1.26.x/advanced-usage.html#ssl-warnings
  warnings.warn(
2025-07-21 18:29:30,811 INFO aipaas_engine [api_client.py-request-190] [p-137 t-139764304869184] (instance_id:255e6ef2-86e7-42c3-a456-6f05a56a9554 aipaas_uri:aipaas_uri ip:************ trace_id:c1f74606) request url: http://console-kwe.his-beta.huawei.com/ailabgateway/tenant/aipaas/deploying/admin/com.huawei.finance.ai.opt.fop/api/v1/online/task/notice
2025-07-21 18:29:30,811 INFO aipaas_engine [api_client.py-request-191] [p-137 t-139764304869184] (instance_id:255e6ef2-86e7-42c3-a456-6f05a56a9554 aipaas_uri:aipaas_uri ip:************ trace_id:c1f74606) request auth info: {"roleCode": "AILabInternalCaller", "enterprise": "88888888888888888888888888888888", "project": "00000000000000000000000000000005"}
2025-07-21 18:29:30,811 INFO aipaas_engine [api_client.py-request-193] [p-137 t-139764304869184] (instance_id:255e6ef2-86e7-42c3-a456-6f05a56a9554 aipaas_uri:aipaas_uri ip:************ trace_id:c1f74606) request params: {'publishId': '255e6ef2-86e7-42c3-a456-6f05a56a9554', 'ip': '************', 'msg': '.hic.cloud port=8000 sslmode=require\', closed: 2> (reason: OperationalError:SSL SYSCALL error: EOF detected\n)\n2025-07-21 18:29:22 log.py [line:130] ERROR: Exception during reset or similar\nTraceback (most recent call last):\nFile "/usr/local/lib/python3.9/site-packages/sqlalchemy/pool/base.py", line 682, in _finalize_fairy\nfairy._reset(pool)\nFile "/usr/local/lib/python3.9/site-packages/sqlalchemy/pool/base.py", line 887, in _reset\npool._dialect.do_rollback(self)\nFile "/usr/local/lib/python3.9/site-packages/sqlalchemy/engine/default.py", line 667, in do_rollback\ndbapi_connection.rollback()\npsycopg2.OperationalError: SSL SYSCALL error: EOF detected\n\n2025-07-21 18:29:22 log.py [line:117] INFO: Invalidate connection <connection object at 0x7f42daf153d0; dsn: \'user=fin_dm_opt_fop password=xxx dbname=fin_opt_uat_d1 host=dws-fin-opt-test-cls-dws.beta.hic.cloud port=8000 sslmode=require\', closed: 2> (reason: OperationalError:SSL SYSCALL error: EOF detected\n).,error info:业务服务挂掉重启,error code:1405'}
2025-07-21 18:29:31 start start_all.sh
all envs:\n AIPAAS_ENV=beta3 AIPAAS_TASK_TYPE=deploy AIPAAS_CONSOLE_SWITCH=1 AIPAAS_APP_ID=com.huawei.finance.ai.opt.fop AIPAAS_LOGS_PATH=/logs/com.huawei.finance.ai.opt.fop/255e6ef2-86e7-42c3-a456-6f05a56a9554 AIPAAS_CLUSTER_PROVIDER=csb_ma AIPAAS_INSTANCE_ID=255e6ef2-86e7-42c3-a456-6f05a56a9554 AIPAAS_DEPLOY_PORT=80 AIPAAS_METADATA_API_URL=http://console-kwe.his-beta.huawei.com/ailabgateway/tenant/aipaas/deploying
AIPAAS_AI_ENGINE 
INSTANCE_ID 255e6ef2-86e7-42c3-a456-6f05a56a9554
POD_ID c90d73f6-b520-44f7-96b3-c6bc0c636077-e2cb7pf8-547b466cdd-frhj4
pip version: pip 22.2.2 from /usr/local/lib/python3.9/site-packages/pip (python 3.9)
pip3 version: pip 22.2.2 from /usr/local/lib/python3.9/site-packages/pip (python 3.9)
pip 22.2.2 from /usr/local/lib/python3.9/site-packages/pip (python 3.9)
using pip3 default
python version: Python 3.9.6
python3 version: Python 3.9.6
Python 3.9.6
using python3 default
export environment
check python and pip version
python3 version: Python 3.9.6
pip3 version: pip 22.2.2 from /usr/local/lib/python3.9/site-packages/pip (python 3.9)
pip3 config list: :env:.extra-index-url=''
global.index-url='http://mirrors.tools.huawei.com/pypi/simple'
global.timeout='120'
global.trusted-host='mirrors.tools.huawei.com'
os info: Linux c90d73f6-b520-44f7-96b3-c6bc0c636077-e2cb7pf8-547b466cdd-frhj4 4.18.0-*********.h1305.eulerosv2r9.x86_64 #1 SMP Wed May 29 09:00:28 UTC 2024 x86_64 x86_64 x86_64 GNU/Linux
set pip mirrors
Looking in indexes: https://mirrors.tools.huawei.com/pypi/simple
Requirement already satisfied: pip!=21.3.1,<23.0,>=20.3.4 in /usr/local/lib/python3.9/site-packages (22.2.2)
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv

[notice] A new release of pip available: 22.2.2 -> 25.1.1
[notice] To update, run: pip install --upgrade pip
:env:.extra-index-url=''
global.index-url='http://mirrors.tools.huawei.com/pypi/simple'
global.timeout='120'
global.trusted-host='mirrors.tools.huawei.com'
Writing to /etc/pip.conf
Writing to /etc/pip.conf
Writing to /etc/pip.conf
Writing to /etc/pip.conf
Writing to /etc/pip.conf
Writing to /root/.config/pip/pip.conf
Writing to /root/.config/pip/pip.conf
Writing to /root/.config/pip/pip.conf
Writing to /root/.config/pip/pip.conf
Writing to /root/.config/pip/pip.conf
Writing to /usr/local/pip.conf
Writing to /usr/local/pip.conf
Writing to /usr/local/pip.conf
Writing to /usr/local/pip.conf
Writing to /usr/local/pip.conf
:env:.extra-index-url=''
global.extra-index-url='https://pypi.cloudartifact.dgg.dragon.tools.huawei.com/artifactory/api/pypi/pypi-ailab/simple'
global.index-url='https://mirrors.tools.huawei.com/pypi/simple'
global.timeout='120'
global.trusted-host='mirrors.tools.huawei.com pypi.cloudartifact.dgg.dragon.tools.huawei.com'
install.trusted-host='mirrors.tools.huawei.com pypi.cloudartifact.dgg.dragon.tools.huawei.com'
set git
git version: git version 2.21.0
set time zoneinfo
current date: Mon Jul 21 18:29:38 CST 2025
current date: Mon Jul 21 18:29:38 CST 2025
rewrite hosts
create common directory
starting uninstall old AIPaas SDK
WARNING: Skipping aipaas as it is not installed.
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv
WARNING: Skipping aipaas-core as it is not installed.
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv
WARNING: Skipping aipaas-lab as it is not installed.
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv
WARNING: Skipping hw-ailab-aipaas as it is not installed.
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv
WARNING: Skipping hw-ailab-aipaas-core as it is not installed.
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv
WARNING: Skipping hw-ailab-aipaas-lab as it is not installed.
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv
WARNING: Skipping hw-ailab-aipaas-client as it is not installed.
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv
WARNING: Skipping hw-ailab-aipaas-engine as it is not installed.
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv
AIPaas SDK normal version==========
starting install AIPaas SDK
AIPaas Client version: hw-ailab-aipaas-client==1.0.4
AIPaas Engine version: hw-ailab-aipaas-engine==1.0.4
AIPaas Task version: hw-ailab-model_monitor==1.7.0
Looking in indexes: https://mirrors.tools.huawei.com/pypi/simple, https://pypi.cloudartifact.dgg.dragon.tools.huawei.com/artifactory/api/pypi/pypi-ailab/simple
Collecting hw-ailab-aipaas-client==1.0.4
  Downloading https://pypi.cloudartifact.dgg.dragon.tools.huawei.com/artifactory/api/pypi/pypi-ailab/hw-ailab-aipaas-client/1.0.4/hw_ailab_aipaas_client-1.0.4-py3-none-any.whl (55 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 55.8/55.8 kB 166.9 MB/s eta 0:00:00
Collecting gunicorn==20.1.0
  Downloading https://mirrors.tools.huawei.com/pypi/packages/e4/dd/5b190393e6066286773a67dfcc2f9492058e9b57c4867a95f1ba5caf0a83/gunicorn-20.1.0-py3-none-any.whl (79 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 79.5/79.5 kB 1.1 MB/s eta 0:00:00
Collecting gevent==22.10.2
  Downloading https://mirrors.tools.huawei.com/pypi/packages/76/eb/69d278e3b29dfa081a7af0b16ec616ea43fa68854c258713f6c1c52ebd22/gevent-22.10.2-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (6.4 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 6.4/6.4 MB 24.0 MB/s eta 0:00:00
Collecting Werkzeug==2.2.3
  Downloading https://mirrors.tools.huawei.com/pypi/packages/f6/f8/9da63c1617ae2a1dec2fbf6412f3a0cfe9d4ce029eccbda6e1e4258ca45f/Werkzeug-2.2.3-py3-none-any.whl (233 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 233.6/233.6 kB 45.4 MB/s eta 0:00:00
Collecting sqlalchemy<=1.4.22
  Downloading https://mirrors.tools.huawei.com/pypi/packages/4a/ab/c3dfbfa29c481a1e089266825b0e131ae49554f4bc4bf485532787d604d5/SQLAlchemy-1.4.22-cp39-cp39-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.6 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.6/1.6 MB 15.0 MB/s eta 0:00:00
Collecting concurrent-log-handler==0.9.19
  Downloading https://mirrors.tools.huawei.com/pypi/packages/13/12/6e181ad4e66b3e0195f76c3099d44f30db2bb79f8a350f10fdedd87a90ef/concurrent_log_handler-0.9.19-py2.py3-none-any.whl (18 kB)
Collecting psutil<=5.9.0
  Downloading https://mirrors.tools.huawei.com/pypi/packages/c4/35/7cec9647be077784d20913404f914fffd8fe6dfd0673e29f7bd822ac1331/psutil-5.9.0-cp39-cp39-manylinux_2_12_x86_64.manylinux2010_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (280 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 280.4/280.4 kB 48.4 MB/s eta 0:00:00
Collecting deprecated<=1.2.13
  Downloading https://mirrors.tools.huawei.com/pypi/packages/51/6a/c3a0408646408f7283b7bc550c30a32cc791181ec4618592eec13e066ce3/Deprecated-1.2.13-py2.py3-none-any.whl (9.6 kB)
Collecting requests<=2.28.1,>=2.25.1
  Downloading https://mirrors.tools.huawei.com/pypi/packages/ca/91/6d9b8ccacd0412c08820f72cebaa4f0c0441b5cda699c90f618b6f8a1b42/requests-2.28.1-py3-none-any.whl (62 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 62.8/62.8 kB 192.1 MB/s eta 0:00:00
Collecting greenlet==2.0.2
  Downloading https://mirrors.tools.huawei.com/pypi/packages/e9/29/2ae545c4c0218b042c2bb0760c0f65e114cca1ab5e552dc23b0f118e428a/greenlet-2.0.2-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (610 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 610.9/610.9 kB 16.6 MB/s eta 0:00:00
Collecting pytz==2021.1
  Downloading https://mirrors.tools.huawei.com/pypi/packages/70/94/784178ca5dd892a98f113cdd923372024dc04b8d40abe77ca76b5fb90ca6/pytz-2021.1-py2.py3-none-any.whl (510 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 510.8/510.8 kB 69.0 MB/s eta 0:00:00
Collecting setuptools==45.3.0
  Downloading https://mirrors.tools.huawei.com/pypi/packages/0e/8e/4d9a9009afeae48ec1301713d96b9ae901aa6e157637ddf37e844c1bf4ee/setuptools-45.3.0-py3-none-any.whl (585 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 585.5/585.5 kB 4.3 MB/s eta 0:00:00
Collecting flask==2.2.5
  Downloading https://mirrors.tools.huawei.com/pypi/packages/9f/1a/8b6d48162861009d1e017a9740431c78d860809773b66cac220a11aa3310/Flask-2.2.5-py3-none-any.whl (101 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 101.8/101.8 kB 75.1 MB/s eta 0:00:00
Collecting certifi<=2021.5.30
  Downloading https://mirrors.tools.huawei.com/pypi/packages/05/1b/0a0dece0e8aa492a6ec9e4ad2fe366b511558cdc73fd3abc82ba7348e875/certifi-2021.5.30-py2.py3-none-any.whl (145 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 145.5/145.5 kB 36.1 MB/s eta 0:00:00
Collecting pyjwt==1.7.1
  Downloading https://mirrors.tools.huawei.com/pypi/packages/87/8b/6a9f14b5f781697e51259d81657e6048fd31a113229cf346880bb7545565/PyJWT-1.7.1-py2.py3-none-any.whl (18 kB)
Collecting cryptography==39.0.2
  Downloading https://mirrors.tools.huawei.com/pypi/packages/26/d2/85480f4e754375c6d8e4a18cc8d2f28ef1984cf2843395c4d1ea396331d3/cryptography-39.0.2-cp36-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (4.2 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 4.2/4.2 MB 20.5 MB/s eta 0:00:00
Collecting pyOpenSSL==23.0.0
  Downloading https://mirrors.tools.huawei.com/pypi/packages/73/00/b78f9fae05bb1633f7209aa394fa0c3563ef760ab7f47ac37768bf4e4d78/pyOpenSSL-23.0.0-py3-none-any.whl (57 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 57.3/57.3 kB 157.3 MB/s eta 0:00:00
Collecting Jinja2==3.1.2
  Downloading https://mirrors.tools.huawei.com/pypi/packages/bc/c3/f068337a370801f372f2f8f6bad74a5c140f6fda3d9de154052708dd3c65/Jinja2-3.1.2-py3-none-any.whl (133 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 133.1/133.1 kB 219.0 MB/s eta 0:00:00
Collecting urllib3==1.26.9
  Downloading https://mirrors.tools.huawei.com/pypi/packages/ec/03/062e6444ce4baf1eac17a6a0ebfe36bb1ad05e1df0e20b110de59c278498/urllib3-1.26.9-py2.py3-none-any.whl (138 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 139.0/139.0 kB 56.2 MB/s eta 0:00:00
Collecting click==8.1.3
  Downloading https://mirrors.tools.huawei.com/pypi/packages/c2/f1/df59e28c642d583f7dacffb1e0965d0e00b218e0186d7858ac5233dce840/click-8.1.3-py3-none-any.whl (96 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 96.6/96.6 kB 232.1 MB/s eta 0:00:00
Collecting esdk-obs-python==3.22.2
  Downloading https://mirrors.tools.huawei.com/pypi/packages/50/19/9997c9b62c3ce3c743da98629ff2f5a0c913368b0485501e6a0a89b1a73e/esdk-obs-python-3.22.2.tar.gz (79 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 79.9/79.9 kB 98.6 MB/s eta 0:00:00
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Collecting cacheout<=0.14.1
  Downloading https://mirrors.tools.huawei.com/pypi/packages/a5/5c/7897793f5da5bb56cedda6aa541ceb5a06468445f44e6f1cab4ab25d0b6f/cacheout-0.14.1-py3-none-any.whl (18 kB)
Collecting itsdangerous==2.1.2
  Downloading https://mirrors.tools.huawei.com/pypi/packages/68/5f/447e04e828f47465eeab35b5d408b7ebaaaee207f48b7136c5a7267a30ae/itsdangerous-2.1.2-py3-none-any.whl (15 kB)
Collecting MarkupSafe==2.1.1
  Downloading https://mirrors.tools.huawei.com/pypi/packages/df/06/c515c5bc43b90462e753bc768e6798193c6520c9c7eb2054c7466779a9db/MarkupSafe-2.1.1-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (25 kB)
Collecting pyyaml>=5.3.1
  Downloading https://mirrors.tools.huawei.com/pypi/packages/3d/32/e7bd8535d22ea2874cef6a81021ba019474ace0d13a4819c2a4bce79bd6a/PyYAML-6.0.2-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (737 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 737.4/737.4 kB 20.9 MB/s eta 0:00:00
Collecting fs-s3fs==1.1.1
  Downloading https://mirrors.tools.huawei.com/pypi/packages/14/71/9b36a6dbd28386e2028e4ab9aac9c30874fc9c74d6b8fa0c8c2806548311/fs_s3fs-1.1.1-py2.py3-none-any.whl (9.7 kB)
Collecting openpyxl<=3.0.9
  Downloading https://mirrors.tools.huawei.com/pypi/packages/1c/a6/8ce4d2ef2c29be3235c08bb00e0b81e29d38ebc47d82b17af681bf662b74/openpyxl-3.0.9-py2.py3-none-any.whl (242 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 242.2/242.2 kB 267.5 MB/s eta 0:00:00
Collecting portalocker>=1.4.0
  Downloading https://mirrors.tools.huawei.com/pypi/packages/4b/a6/38c8e2f318bf67d338f4d629e93b0b4b9af331f455f0390ea8ce4a099b26/portalocker-3.2.0-py3-none-any.whl (22 kB)
Collecting cffi>=1.12
  Downloading https://mirrors.tools.huawei.com/pypi/packages/bd/62/a1f468e5708a70b1d86ead5bab5520861d9c7eacce4a885ded9faa7729c3/cffi-1.17.1-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (445 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 445.2/445.2 kB 13.3 MB/s eta 0:00:00
Collecting pycryptodome==3.10.1
  Downloading https://mirrors.tools.huawei.com/pypi/packages/ad/16/9627ab0493894a11c68e46000dbcc82f578c8ff06bc2980dcd016aea9bd3/pycryptodome-3.10.1-cp35-abi3-manylinux2010_x86_64.whl (1.9 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.9/1.9 MB 27.0 MB/s eta 0:00:00
Collecting importlib-metadata>=3.6.0
  Downloading https://mirrors.tools.huawei.com/pypi/packages/20/b0/36bd937216ec521246249be3bf9855081de4c5e06a0c9b4219dbeda50373/importlib_metadata-8.7.0-py3-none-any.whl (27 kB)
Collecting boto3~=1.9
  Downloading https://mirrors.tools.huawei.com/pypi/packages/0b/82/faf1b3bab5a94c3a3cf9ddc8e04eee2bdc29d5b8da42d7cda905c505e311/boto3-1.39.9-py3-none-any.whl (139 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 139.9/139.9 kB 3.8 MB/s eta 0:00:00
Collecting six~=1.10
  Downloading https://mirrors.tools.huawei.com/pypi/packages/b7/ce/149a00dd41f10bc29e5921b496af8b574d8413afcd5e30dfa0ed46c2cc5e/six-1.17.0-py2.py3-none-any.whl (11 kB)
Collecting fs~=2.4
  Downloading https://mirrors.tools.huawei.com/pypi/packages/b9/5c/a3d95dc1ec6cdeb032d789b552ecc76effa3557ea9186e1566df6aac18df/fs-2.4.16-py2.py3-none-any.whl (135 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 135.3/135.3 kB 219.3 MB/s eta 0:00:00
Collecting zope.event
  Downloading https://mirrors.tools.huawei.com/pypi/packages/00/ed/d8c3f56c1edb0ee9b51461dd08580382e9589850f769b69f0dedccff5215/zope_event-5.1-py3-none-any.whl (6.9 kB)
Collecting zope.interface
  Downloading https://mirrors.tools.huawei.com/pypi/packages/88/d4/4ba1569b856870527cec4bf22b91fe704b81a3c1a451b2ccf234e9e0666f/zope.interface-7.2-cp39-cp39-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (253 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 253.8/253.8 kB 7.2 MB/s eta 0:00:00
Collecting wrapt<2,>=1.10
  Downloading https://mirrors.tools.huawei.com/pypi/packages/bf/bb/d552bfe47db02fcfc950fc563073a33500f8108efa5f7b41db2f83a59028/wrapt-1.17.2-cp39-cp39-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (82 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 82.6/82.6 kB 198.7 MB/s eta 0:00:00
Collecting et-xmlfile
  Downloading https://mirrors.tools.huawei.com/pypi/packages/c1/8b/5fe2cc11fee489817272089c4203e679c63b570a5aaeb18d852ae3cbba6a/et_xmlfile-2.0.0-py3-none-any.whl (18 kB)
Collecting charset-normalizer<3,>=2
  Downloading https://mirrors.tools.huawei.com/pypi/packages/db/51/a507c856293ab05cdc1db77ff4bc1268ddd39f29e7dc4919aa497f0adbec/charset_normalizer-2.1.1-py3-none-any.whl (39 kB)
Collecting idna<4,>=2.5
  Downloading https://mirrors.tools.huawei.com/pypi/packages/76/c6/c88e154df9c4e1a2a66ccf0005a88dfb2650c1dffb6f5ce603dfbd452ce3/idna-3.10-py3-none-any.whl (70 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 70.4/70.4 kB 193.3 MB/s eta 0:00:00
Collecting s3transfer<0.14.0,>=0.13.0
  Downloading https://mirrors.tools.huawei.com/pypi/packages/6d/4f/d073e09df851cfa251ef7840007d04db3293a0482ce607d2b993926089be/s3transfer-0.13.1-py3-none-any.whl (85 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 85.3/85.3 kB 180.1 MB/s eta 0:00:00
Collecting botocore<1.40.0,>=1.39.9
  Downloading https://mirrors.tools.huawei.com/pypi/packages/07/2d/951352b2a14e70144c1a4dff611472a6213b8dbdb7996c0029caa48c80e3/botocore-1.39.9-py3-none-any.whl (13.9 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 13.9/13.9 MB 5.0 MB/s eta 0:00:00
Collecting jmespath<2.0.0,>=0.7.1
  Downloading https://mirrors.tools.huawei.com/pypi/packages/31/b4/b9b800c45527aadd64d5b442f9b932b00648617eb5d63d2c7a6587b7cafc/jmespath-1.0.1-py3-none-any.whl (20 kB)
Collecting pycparser
  Downloading https://mirrors.tools.huawei.com/pypi/packages/13/a3/a812df4e2dd5696d1f351d58b8fe16a405b234ad2886a0dab9183fb78109/pycparser-2.22-py3-none-any.whl (117 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 117.6/117.6 kB 229.5 MB/s eta 0:00:00
Collecting appdirs~=1.4.3
  Downloading https://mirrors.tools.huawei.com/pypi/packages/3b/00/2344469e2084fb287c2e0b57b72910309874c3245463acd6cf5e3db69324/appdirs-1.4.4-py2.py3-none-any.whl (9.6 kB)
Collecting zipp>=3.20
  Downloading https://mirrors.tools.huawei.com/pypi/packages/2e/54/647ade08bf0db230bfea292f893923872fd20be6ac6f53b2b936ba839d75/zipp-3.23.0-py3-none-any.whl (10 kB)
Collecting python-dateutil<3.0.0,>=2.1
  Downloading https://mirrors.tools.huawei.com/pypi/packages/ec/57/56b9bcc3c9c6a792fcbaf139543cee77261f3651ca9da0c93f5c1221264b/python_dateutil-2.9.0.post0-py2.py3-none-any.whl (229 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 229.9/229.9 kB 232.4 MB/s eta 0:00:00
Using legacy 'setup.py install' for esdk-obs-python, since package 'wheel' is not installed.
Installing collected packages: pytz, pyjwt, certifi, appdirs, zipp, wrapt, urllib3, six, setuptools, pyyaml, pycryptodome, pycparser, psutil, portalocker, MarkupSafe, jmespath, itsdangerous, idna, greenlet, et-xmlfile, click, charset-normalizer, cacheout, zope.interface, zope.event, Werkzeug, sqlalchemy, requests, python-dateutil, openpyxl, Jinja2, importlib-metadata, gunicorn, fs, esdk-obs-python, deprecated, concurrent-log-handler, cffi, gevent, flask, cryptography, botocore, s3transfer, pyOpenSSL, boto3, fs-s3fs, hw-ailab-aipaas-client
  Running setup.py install for esdk-obs-python: started
  Running setup.py install for esdk-obs-python: finished with status 'done'
Successfully installed Jinja2-3.1.2 MarkupSafe-2.1.1 Werkzeug-2.2.3 appdirs-1.4.4 boto3-1.39.9 botocore-1.39.9 cacheout-0.14.1 certifi-2021.5.30 cffi-1.17.1 charset-normalizer-2.1.1 click-8.1.3 concurrent-log-handler-0.9.19 cryptography-39.0.2 deprecated-1.2.13 esdk-obs-python-3.22.2 et-xmlfile-2.0.0 flask-2.2.5 fs-2.4.16 fs-s3fs-1.1.1 gevent-22.10.2 greenlet-2.0.2 gunicorn-20.1.0 hw-ailab-aipaas-client-1.0.4 idna-3.10 importlib-metadata-8.7.0 itsdangerous-2.1.2 jmespath-1.0.1 openpyxl-3.0.9 portalocker-3.2.0 psutil-5.9.0 pyOpenSSL-23.0.0 pycparser-2.22 pycryptodome-3.10.1 pyjwt-1.7.1 python-dateutil-2.9.0.post0 pytz-2022.2.1 pyyaml-6.0.2 requests-2.28.1 s3transfer-0.13.1 setuptools-56.0.0 six-1.17.0 sqlalchemy-1.4.22 urllib3-1.26.9 wrapt-1.17.2 zipp-3.23.0 zope.event-5.1 zope.interface-7.2
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv

[notice] A new release of pip available: 22.2.2 -> 25.1.1
[notice] To update, run: pip install --upgrade pip
Looking in indexes: https://mirrors.tools.huawei.com/pypi/simple, https://pypi.cloudartifact.dgg.dragon.tools.huawei.com/artifactory/api/pypi/pypi-ailab/simple
Collecting hw-ailab-aipaas-engine[deploy]==1.0.4
  Downloading https://pypi.cloudartifact.dgg.dragon.tools.huawei.com/artifactory/api/pypi/pypi-ailab/hw-ailab-aipaas-engine/1.0.4/hw_ailab_aipaas_engine-1.0.4-py3-none-any.whl (79 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 79.8/79.8 kB 13.7 MB/s eta 0:00:00
Requirement already satisfied: gunicorn==20.1.0 in /usr/local/lib/python3.9/site-packages (from hw-ailab-aipaas-engine[deploy]==1.0.4) (20.1.0)
Collecting pytz==2021.1
  Downloading https://mirrors.tools.huawei.com/pypi/packages/70/94/784178ca5dd892a98f113cdd923372024dc04b8d40abe77ca76b5fb90ca6/pytz-2021.1-py2.py3-none-any.whl (510 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 510.8/510.8 kB 6.9 MB/s eta 0:00:00
Requirement already satisfied: cryptography==39.0.2 in /usr/local/lib/python3.9/site-packages (from hw-ailab-aipaas-engine[deploy]==1.0.4) (39.0.2)
Requirement already satisfied: cacheout<=0.14.1 in /usr/local/lib/python3.9/site-packages (from hw-ailab-aipaas-engine[deploy]==1.0.4) (0.14.1)
Requirement already satisfied: MarkupSafe==2.1.1 in /usr/local/lib/python3.9/site-packages (from hw-ailab-aipaas-engine[deploy]==1.0.4) (2.1.1)
Requirement already satisfied: openpyxl<=3.1.2 in /usr/local/lib/python3.9/site-packages (from hw-ailab-aipaas-engine[deploy]==1.0.4) (3.0.9)
Requirement already satisfied: itsdangerous==2.1.2 in /usr/local/lib/python3.9/site-packages (from hw-ailab-aipaas-engine[deploy]==1.0.4) (2.1.2)
Requirement already satisfied: psutil<=5.9.0 in /usr/local/lib/python3.9/site-packages (from hw-ailab-aipaas-engine[deploy]==1.0.4) (5.9.0)
Requirement already satisfied: flask==2.2.5 in /usr/local/lib/python3.9/site-packages (from hw-ailab-aipaas-engine[deploy]==1.0.4) (2.2.5)
Requirement already satisfied: greenlet==2.0.2 in /usr/local/lib/python3.9/site-packages (from hw-ailab-aipaas-engine[deploy]==1.0.4) (2.0.2)
Requirement already satisfied: certifi<=2021.5.30 in /usr/local/lib/python3.9/site-packages (from hw-ailab-aipaas-engine[deploy]==1.0.4) (2021.5.30)
Requirement already satisfied: urllib3==1.26.9 in /usr/local/lib/python3.9/site-packages (from hw-ailab-aipaas-engine[deploy]==1.0.4) (1.26.9)
Requirement already satisfied: pyjwt==1.7.1 in /usr/local/lib/python3.9/site-packages (from hw-ailab-aipaas-engine[deploy]==1.0.4) (1.7.1)
Requirement already satisfied: Jinja2==3.1.2 in /usr/local/lib/python3.9/site-packages (from hw-ailab-aipaas-engine[deploy]==1.0.4) (3.1.2)
Requirement already satisfied: Werkzeug==2.2.3 in /usr/local/lib/python3.9/site-packages (from hw-ailab-aipaas-engine[deploy]==1.0.4) (2.2.3)
Requirement already satisfied: esdk-obs-python==3.22.2 in /usr/local/lib/python3.9/site-packages (from hw-ailab-aipaas-engine[deploy]==1.0.4) (3.22.2)
Requirement already satisfied: concurrent-log-handler==0.9.19 in /usr/local/lib/python3.9/site-packages (from hw-ailab-aipaas-engine[deploy]==1.0.4) (0.9.19)
Requirement already satisfied: deprecated<=1.2.13 in /usr/local/lib/python3.9/site-packages (from hw-ailab-aipaas-engine[deploy]==1.0.4) (1.2.13)
Collecting setuptools==45.3.0
  Downloading https://mirrors.tools.huawei.com/pypi/packages/0e/8e/4d9a9009afeae48ec1301713d96b9ae901aa6e157637ddf37e844c1bf4ee/setuptools-45.3.0-py3-none-any.whl (585 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 585.5/585.5 kB 14.0 MB/s eta 0:00:00
Requirement already satisfied: requests<=2.28.1,>=2.25.1 in /usr/local/lib/python3.9/site-packages (from hw-ailab-aipaas-engine[deploy]==1.0.4) (2.28.1)
Requirement already satisfied: click==8.1.3 in /usr/local/lib/python3.9/site-packages (from hw-ailab-aipaas-engine[deploy]==1.0.4) (8.1.3)
Requirement already satisfied: pyOpenSSL==23.0.0 in /usr/local/lib/python3.9/site-packages (from hw-ailab-aipaas-engine[deploy]==1.0.4) (23.0.0)
Requirement already satisfied: sqlalchemy<=1.4.22 in /usr/local/lib/python3.9/site-packages (from hw-ailab-aipaas-engine[deploy]==1.0.4) (1.4.22)
Requirement already satisfied: fs-s3fs==1.1.1 in /usr/local/lib/python3.9/site-packages (from hw-ailab-aipaas-engine[deploy]==1.0.4) (1.1.1)
Requirement already satisfied: gevent==22.10.2 in /usr/local/lib/python3.9/site-packages (from hw-ailab-aipaas-engine[deploy]==1.0.4) (22.10.2)
Requirement already satisfied: pyyaml>=5.3.1 in /usr/local/lib/python3.9/site-packages (from hw-ailab-aipaas-engine[deploy]==1.0.4) (6.0.2)
Collecting hw-ailab-aipaas-deploy==1.0.4
  Downloading https://pypi.cloudartifact.dgg.dragon.tools.huawei.com/artifactory/api/pypi/pypi-ailab/hw-ailab-aipaas-deploy/1.0.4/hw_ailab_aipaas_deploy-1.0.4-py3-none-any.whl (6.7 kB)
Requirement already satisfied: portalocker>=1.4.0 in /usr/local/lib/python3.9/site-packages (from concurrent-log-handler==0.9.19->hw-ailab-aipaas-engine[deploy]==1.0.4) (3.2.0)
Requirement already satisfied: cffi>=1.12 in /usr/local/lib/python3.9/site-packages (from cryptography==39.0.2->hw-ailab-aipaas-engine[deploy]==1.0.4) (1.17.1)
Requirement already satisfied: pycryptodome==3.10.1 in /usr/local/lib/python3.9/site-packages (from esdk-obs-python==3.22.2->hw-ailab-aipaas-engine[deploy]==1.0.4) (3.10.1)
Requirement already satisfied: importlib-metadata>=3.6.0 in /usr/local/lib/python3.9/site-packages (from flask==2.2.5->hw-ailab-aipaas-engine[deploy]==1.0.4) (8.7.0)
Requirement already satisfied: fs~=2.4 in /usr/local/lib/python3.9/site-packages (from fs-s3fs==1.1.1->hw-ailab-aipaas-engine[deploy]==1.0.4) (2.4.16)
Requirement already satisfied: boto3~=1.9 in /usr/local/lib/python3.9/site-packages (from fs-s3fs==1.1.1->hw-ailab-aipaas-engine[deploy]==1.0.4) (1.39.9)
Requirement already satisfied: six~=1.10 in /usr/local/lib/python3.9/site-packages (from fs-s3fs==1.1.1->hw-ailab-aipaas-engine[deploy]==1.0.4) (1.17.0)
Requirement already satisfied: zope.event in /usr/local/lib/python3.9/site-packages (from gevent==22.10.2->hw-ailab-aipaas-engine[deploy]==1.0.4) (5.1)
Requirement already satisfied: zope.interface in /usr/local/lib/python3.9/site-packages (from gevent==22.10.2->hw-ailab-aipaas-engine[deploy]==1.0.4) (7.2)
Requirement already satisfied: wrapt<2,>=1.10 in /usr/local/lib/python3.9/site-packages (from deprecated<=1.2.13->hw-ailab-aipaas-engine[deploy]==1.0.4) (1.17.2)
Requirement already satisfied: et-xmlfile in /usr/local/lib/python3.9/site-packages (from openpyxl<=3.1.2->hw-ailab-aipaas-engine[deploy]==1.0.4) (2.0.0)
Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.9/site-packages (from requests<=2.28.1,>=2.25.1->hw-ailab-aipaas-engine[deploy]==1.0.4) (3.10)
Requirement already satisfied: charset-normalizer<3,>=2 in /usr/local/lib/python3.9/site-packages (from requests<=2.28.1,>=2.25.1->hw-ailab-aipaas-engine[deploy]==1.0.4) (2.1.1)
Requirement already satisfied: botocore<1.40.0,>=1.39.9 in /usr/local/lib/python3.9/site-packages (from boto3~=1.9->fs-s3fs==1.1.1->hw-ailab-aipaas-engine[deploy]==1.0.4) (1.39.9)
Requirement already satisfied: jmespath<2.0.0,>=0.7.1 in /usr/local/lib/python3.9/site-packages (from boto3~=1.9->fs-s3fs==1.1.1->hw-ailab-aipaas-engine[deploy]==1.0.4) (1.0.1)
Requirement already satisfied: s3transfer<0.14.0,>=0.13.0 in /usr/local/lib/python3.9/site-packages (from boto3~=1.9->fs-s3fs==1.1.1->hw-ailab-aipaas-engine[deploy]==1.0.4) (0.13.1)
Requirement already satisfied: pycparser in /usr/local/lib/python3.9/site-packages (from cffi>=1.12->cryptography==39.0.2->hw-ailab-aipaas-engine[deploy]==1.0.4) (2.22)
Requirement already satisfied: appdirs~=1.4.3 in /usr/local/lib/python3.9/site-packages (from fs~=2.4->fs-s3fs==1.1.1->hw-ailab-aipaas-engine[deploy]==1.0.4) (1.4.4)
Requirement already satisfied: zipp>=3.20 in /usr/local/lib/python3.9/site-packages (from importlib-metadata>=3.6.0->flask==2.2.5->hw-ailab-aipaas-engine[deploy]==1.0.4) (3.23.0)
Requirement already satisfied: python-dateutil<3.0.0,>=2.1 in /usr/local/lib/python3.9/site-packages (from botocore<1.40.0,>=1.39.9->boto3~=1.9->fs-s3fs==1.1.1->hw-ailab-aipaas-engine[deploy]==1.0.4) (2.9.0.post0)
Installing collected packages: pytz, setuptools, hw-ailab-aipaas-deploy, hw-ailab-aipaas-engine
  Attempting uninstall: pytz
    Found existing installation: pytz 2022.2.1
    Uninstalling pytz-2022.2.1:
      Successfully uninstalled pytz-2022.2.1
  Attempting uninstall: setuptools
    Found existing installation: setuptools 56.0.0
    Uninstalling setuptools-56.0.0:
      Successfully uninstalled setuptools-56.0.0
Successfully installed hw-ailab-aipaas-deploy-1.0.4 hw-ailab-aipaas-engine-1.0.4 pytz-2021.1 setuptools-45.3.0
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv

[notice] A new release of pip available: 22.2.2 -> 25.1.1
[notice] To update, run: pip install --upgrade pip
2025-07-21 18:30:11,372 INFO aipaas_engine [agent.py-main-29] [p-137 t-140668011378496] (instance_id:255e6ef2-86e7-42c3-a456-6f05a56a9554 aipaas_uri:aipaas_uri ip:************ trace_id:9ca67a31) 启动：aipaas_deploy.main
2025-07-21 18:30:11,962 INFO aipaas_engine [main.py-start-26] [p-137 t-140668011378496] (instance_id:255e6ef2-86e7-42c3-a456-6f05a56a9554 aipaas_uri:aipaas_uri ip:************ trace_id:9ca67a31) 启动推理服务任务
2025-07-21 18:30:12,004 INFO aipaas_engine [api_client.py-notice-46] [p-137 t-140668011378496] (instance_id:255e6ef2-86e7-42c3-a456-6f05a56a9554 aipaas_uri:aipaas_uri ip:************ trace_id:9ca67a31) report notice: AIPAAS_INSTANCE_ID=255e6ef2-86e7-42c3-a456-6f05a56a9554.
/usr/local/lib/python3.9/site-packages/urllib3/connectionpool.py:1043: InsecureRequestWarning: Unverified HTTPS request is being made to host 'iam.his-op-beta.huawei.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/1.26.x/advanced-usage.html#ssl-warnings
  warnings.warn(
2025-07-21 18:30:12,031 INFO aipaas_engine [api_client.py-request-190] [p-137 t-140668011378496] (instance_id:255e6ef2-86e7-42c3-a456-6f05a56a9554 aipaas_uri:aipaas_uri ip:************ trace_id:9ca67a31) request url: http://console-kwe.his-beta.huawei.com/ailabgateway/tenant/aipaas/deploying/admin/com.huawei.finance.ai.opt.fop/api/v1/online/task/notice
2025-07-21 18:30:12,031 INFO aipaas_engine [api_client.py-request-191] [p-137 t-140668011378496] (instance_id:255e6ef2-86e7-42c3-a456-6f05a56a9554 aipaas_uri:aipaas_uri ip:************ trace_id:9ca67a31) request auth info: {"roleCode": "AILabInternalCaller", "enterprise": "88888888888888888888888888888888", "project": "00000000000000000000000000000005"}
2025-07-21 18:30:12,032 INFO aipaas_engine [api_client.py-request-193] [p-137 t-140668011378496] (instance_id:255e6ef2-86e7-42c3-a456-6f05a56a9554 aipaas_uri:aipaas_uri ip:************ trace_id:9ca67a31) request params: {'publishId': '255e6ef2-86e7-42c3-a456-6f05a56a9554', 'ip': '************', 'msg': 'AIPAAS_INSTANCE_ID=255e6ef2-86e7-42c3-a456-6f05a56a9554'}
2025-07-21 18:30:12,191 INFO aipaas_engine [api_client.py-notice-46] [p-137 t-140668011378496] (instance_id:255e6ef2-86e7-42c3-a456-6f05a56a9554 aipaas_uri:aipaas_uri ip:************ trace_id:9ca67a31) report notice: step1: task begin..
2025-07-21 18:30:12,192 INFO aipaas_engine [api_client.py-request-190] [p-137 t-140668011378496] (instance_id:255e6ef2-86e7-42c3-a456-6f05a56a9554 aipaas_uri:aipaas_uri ip:************ trace_id:9ca67a31) request url: http://console-kwe.his-beta.huawei.com/ailabgateway/tenant/aipaas/deploying/admin/com.huawei.finance.ai.opt.fop/api/v1/online/task/notice
2025-07-21 18:30:12,192 INFO aipaas_engine [api_client.py-request-191] [p-137 t-140668011378496] (instance_id:255e6ef2-86e7-42c3-a456-6f05a56a9554 aipaas_uri:aipaas_uri ip:************ trace_id:9ca67a31) request auth info: {"roleCode": "AILabInternalCaller", "enterprise": "88888888888888888888888888888888", "project": "00000000000000000000000000000005"}
2025-07-21 18:30:12,192 INFO aipaas_engine [api_client.py-request-193] [p-137 t-140668011378496] (instance_id:255e6ef2-86e7-42c3-a456-6f05a56a9554 aipaas_uri:aipaas_uri ip:************ trace_id:9ca67a31) request params: {'publishId': '255e6ef2-86e7-42c3-a456-6f05a56a9554', 'ip': '************', 'msg': 'step1: task begin.'}
2025-07-21 18:30:12,344 INFO aipaas_engine [api_client.py-state-30] [p-137 t-140668011378496] (instance_id:255e6ef2-86e7-42c3-a456-6f05a56a9554 aipaas_uri:aipaas_uri ip:************ trace_id:9ca67a31) report state: init.
2025-07-21 18:30:12,344 INFO aipaas_engine [api_client.py-request-190] [p-137 t-140668011378496] (instance_id:255e6ef2-86e7-42c3-a456-6f05a56a9554 aipaas_uri:aipaas_uri ip:************ trace_id:9ca67a31) request url: http://console-kwe.his-beta.huawei.com/ailabgateway/tenant/aipaas/deploying/admin/com.huawei.finance.ai.opt.fop/api/v1/online/task/updateState
2025-07-21 18:30:12,345 INFO aipaas_engine [api_client.py-request-191] [p-137 t-140668011378496] (instance_id:255e6ef2-86e7-42c3-a456-6f05a56a9554 aipaas_uri:aipaas_uri ip:************ trace_id:9ca67a31) request auth info: {"roleCode": "AILabInternalCaller", "enterprise": "88888888888888888888888888888888", "project": "00000000000000000000000000000005"}
2025-07-21 18:30:12,345 INFO aipaas_engine [api_client.py-request-193] [p-137 t-140668011378496] (instance_id:255e6ef2-86e7-42c3-a456-6f05a56a9554 aipaas_uri:aipaas_uri ip:************ trace_id:9ca67a31) request params: {'publishId': '255e6ef2-86e7-42c3-a456-6f05a56a9554', 'ip': '************', 'port': 8080, 'logPath': '/logs/com.huawei.finance.ai.opt.fop/255e6ef2-86e7-42c3-a456-6f05a56a9554/deploy_************.log', 'state': 'init', 'rollingId': '', 'msg': 'step1: task begin.'}
2025-07-21 18:30:12,576 INFO aipaas_engine [api_client.py-get_metainfo-23] [p-137 t-140668011378496] (instance_id:255e6ef2-86e7-42c3-a456-6f05a56a9554 aipaas_uri:aipaas_uri ip:************ trace_id:9ca67a31) get metadata.
2025-07-21 18:30:12,576 INFO aipaas_engine [api_client.py-request-190] [p-137 t-140668011378496] (instance_id:255e6ef2-86e7-42c3-a456-6f05a56a9554 aipaas_uri:aipaas_uri ip:************ trace_id:9ca67a31) request url: http://console-kwe.his-beta.huawei.com/ailabgateway/tenant/aipaas/deploying/admin/com.huawei.finance.ai.opt.fop/api/v1/online/task/metadata/255e6ef2-86e7-42c3-a456-6f05a56a9554
2025-07-21 18:30:12,576 INFO aipaas_engine [api_client.py-request-191] [p-137 t-140668011378496] (instance_id:255e6ef2-86e7-42c3-a456-6f05a56a9554 aipaas_uri:aipaas_uri ip:************ trace_id:9ca67a31) request auth info: {"roleCode": "AILabInternalCaller", "enterprise": "88888888888888888888888888888888", "project": "00000000000000000000000000000005"}
2025-07-21 18:30:12,577 INFO aipaas_engine [api_client.py-request-193] [p-137 t-140668011378496] (instance_id:255e6ef2-86e7-42c3-a456-6f05a56a9554 aipaas_uri:aipaas_uri ip:************ trace_id:9ca67a31) request params: None
2025-07-21 18:30:13,108 INFO aipaas_engine [api_client.py-notice-46] [p-137 t-140668011378496] (instance_id:255e6ef2-86e7-42c3-a456-6f05a56a9554 aipaas_uri:aipaas_uri ip:************ trace_id:9ca67a31) report notice: step2: get metadata success..
2025-07-21 18:30:13,108 INFO aipaas_engine [api_client.py-request-190] [p-137 t-140668011378496] (instance_id:255e6ef2-86e7-42c3-a456-6f05a56a9554 aipaas_uri:aipaas_uri ip:************ trace_id:9ca67a31) request url: http://console-kwe.his-beta.huawei.com/ailabgateway/tenant/aipaas/deploying/admin/com.huawei.finance.ai.opt.fop/api/v1/online/task/notice
2025-07-21 18:30:13,108 INFO aipaas_engine [api_client.py-request-191] [p-137 t-140668011378496] (instance_id:255e6ef2-86e7-42c3-a456-6f05a56a9554 aipaas_uri:aipaas_uri ip:************ trace_id:9ca67a31) request auth info: {"roleCode": "AILabInternalCaller", "enterprise": "88888888888888888888888888888888", "project": "00000000000000000000000000000005"}
2025-07-21 18:30:13,108 INFO aipaas_engine [api_client.py-request-193] [p-137 t-140668011378496] (instance_id:255e6ef2-86e7-42c3-a456-6f05a56a9554 aipaas_uri:aipaas_uri ip:************ trace_id:9ca67a31) request params: {'publishId': '255e6ef2-86e7-42c3-a456-6f05a56a9554', 'ip': '************', 'msg': 'step2: get metadata success.'}
2025-07-21 18:30:13,267 INFO aipaas_engine [download.py-donwload_from_git-328] [p-137 t-140668011378496] (instance_id:255e6ef2-86e7-42c3-a456-6f05a56a9554 aipaas_uri:aipaas_uri ip:************ trace_id:9ca67a31) git clone project.
2025-07-21 18:30:13,271 INFO aipaas_engine [utils.py-run_subprocess-619] [p-137 t-140668011378496] (instance_id:255e6ef2-86e7-42c3-a456-6f05a56a9554 aipaas_uri:aipaas_uri ip:************ trace_id:9ca67a31) stdout:None Cloning into 'opt.fcst.profits.model'...
