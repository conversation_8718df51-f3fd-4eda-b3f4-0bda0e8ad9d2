import logging
from typing import Tu<PERSON>, List, Dict

import numpy as np
import pandas as pd
from aipaas.logger_factory import logger

from ProfitForecast_expert.profitforecast.func_utils import (
    get_last_year_period_id,
    get_last_quarter_3period_id,
    get_last_year_special_month_period_id,
    get_target_period_level,
    get_last_month_period_id,
    add_target_period
)
from ProfitForecast_expert.profitforecast.utils.default_conf import (
    AI_EQUIP_REV_AMT_FIELD,
    AI_FCST_TBL_NAME,
    AI_MGP_RATE_FIELD,
    CHANYE_EXPERT_FCST_TBL_NAME,
    CHANYE_EXPERT_MARK,
    CHANYE_EXPERT_TYPE,
    COMBINED_TBL_NAME,
    EQUIP_REV_AMT_FIELD,
    FACT_EQUIP_REV_AMT_FIELD,
    FACT_MGP_RATE_FIELD,
    FACT_TBL_NAME,
    DIM_EXPERT_FCST_TBL_NAME,
    DIM_EXPERT_MARK,
    DIM_EXPERT_TYPE,
    MGP_RATE_FIELD,
    NULL_STRING_PLACE_HOLDER,
    VERSION_TBL_NAME
)
from profits_pred.db.get_data import get_data


def get_combine_tbl_names(expert_data_type):
    ai_fcst_tbl_name = AI_FCST_TBL_NAME  # AI预测结果表
    fact_tbl_name = FACT_TBL_NAME        # 真实数据表
    result_tbl_name = COMBINED_TBL_NAME  # 融合结果表
    if expert_data_type == "both":  # 专家预测结果表(根据参数确定是 集团 or/and 产业专家预测数据)
        expert_fcst_tbl_names = [
            DIM_EXPERT_FCST_TBL_NAME,
            CHANYE_EXPERT_FCST_TBL_NAME
        ]
    elif expert_data_type == DIM_EXPERT_TYPE:
        expert_fcst_tbl_names = [
            DIM_EXPERT_FCST_TBL_NAME,
        ]
    elif expert_data_type == CHANYE_EXPERT_TYPE:
        expert_fcst_tbl_names = [
            CHANYE_EXPERT_FCST_TBL_NAME
        ]
    else:
        msg = f"不支持的专家预测结果类型，请检查:{expert_data_type}"
        raise Exception(msg)

    ret_tuple = ai_fcst_tbl_name, expert_fcst_tbl_names, fact_tbl_name, result_tbl_name

    return ret_tuple


def read_ai_table_from_db(ai_fcst_tbl_name, min_period_id):
    sql = f"""
                SELECT period_id, phase_date, target_period, bg_code, bg_name, oversea_code, oversea_desc, 
                    lv1_code, lv1_name, lv2_code, lv2_name,
                    equip_rev_after_fcst, mgp_rate_after_fcst
                FROM {ai_fcst_tbl_name} 
                WHERE period_id >= '{min_period_id}'
                    AND oversea_desc = '全球'
                    AND fcst_type in ('YTD法')
                    AND currency = 'CNY'
                    AND del_flag = 'N'
                """
    ai_fcst_df = get_data(sql)
    ai_fcst_df = ai_fcst_df.drop_duplicates()
    for value_col in ['equip_rev_after_fcst', 'mgp_rate_after_fcst']:
        ai_fcst_df[value_col] = ai_fcst_df[value_col].astype(float)
    logger.info(f"读入AI预测结果表大小:{ai_fcst_df.shape}")
    return ai_fcst_df


def read_expert_table_from_db(expert_fcst_tbl_names, min_period_id):
    expert_fcst_df_list = []
    for tbl_name in expert_fcst_tbl_names:
        if tbl_name == CHANYE_EXPERT_FCST_TBL_NAME:
            sql = f"""SELECT period_id, target_code, 
            bg_code, Case when bg_name='运营商网络'then '运营商' else bg_name end as bg_name, 
            oversea_code, oversea_desc, 
            lv1_code, lv1_name, lv2_code, lv2_name,
            equip_rev_amt, mgp_ratio
            FROM {tbl_name} t1
            WHERE period_id >= '{min_period_id}'
                AND oversea_desc in ('全球')
                AND currency = 'CNY'
                AND del_flag = 'N'
                AND version_code in (
                select max(version_code)
                from {tbl_name} t2
                WHERE period_id >= '{min_period_id}'
                AND oversea_desc in ('全球')
                AND currency = 'CNY'
                AND del_flag = 'N'
                group by period_id
                )
            """
            expert_fcst_df = get_data(sql)
            expert_fcst_df['target_code'] = expert_fcst_df['target_code'].str.replace('SNULL', '', regex=False)
            expert_fcst_df = expert_fcst_df.drop_duplicates()
            for value_col in ['equip_rev_amt', 'mgp_ratio']:
                expert_fcst_df[value_col] = expert_fcst_df[value_col].astype(float)
            logger.info(f"读入{tbl_name}专家预测结果表大小:{expert_fcst_df.shape}")
            expert_fcst_df_list.append(expert_fcst_df)
        elif tbl_name == DIM_EXPERT_FCST_TBL_NAME:
            sql = f"""SELECT period_id, target_code, bg_code, bg_name, oversea_code, oversea_desc, 
            lv1_code, lv1_name, lv2_code, lv2_name,
            equip_rev_amt, mgp_ratio
            FROM {tbl_name}
            WHERE period_id >= '{min_period_id}'
                AND oversea_desc in ('全球')
                AND currency = 'CNY'
                AND del_flag = 'N'
                AND status = 'SUBMIT'
                            """
            expert_fcst_df = get_data(sql)
            expert_fcst_df['target_code'] = expert_fcst_df['target_code'].str.replace('SNULL', '', regex=False)
            expert_fcst_df = expert_fcst_df.drop_duplicates()
            for value_col in ['equip_rev_amt', 'mgp_ratio']:
                expert_fcst_df[value_col] = expert_fcst_df[value_col].astype(float)
            logger.info(f"读入{tbl_name}专家预测结果表大小:{expert_fcst_df.shape}")
            expert_fcst_df_list.append(expert_fcst_df)
    return expert_fcst_df_list


def read_to_combine_data_from_db(ai_fcst_tbl_name: str,
                                 expert_fcst_tbl_names: List[str],
                                 fact_tbl_name: str,
                                 time: str,
                                 run_env: str
                                 ) -> Tuple[pd.DataFrame, List[pd.DataFrame], pd.DataFrame]:
    """
    从数据库中读取待评估和融合的数据，包括：AI预测数据、专家预测数据、真实数据

    Parameters
    ----------
    ai_fcst_tbl_name
        AI预测结果表名
    expert_fcst_tbl_names
        量纲、产业专家预测结果表
    fact_tbl_name
        真实数据表
    time
        当前会计期
    run_env
        当前运行环境: sit, uat, prod

    Returns
    -------

    """
    last_year_period_id = get_last_year_period_id(time)                                    # 去年同期会计期
    last_quarter_3period_ids = get_last_quarter_3period_id(time)                           # 上一季度的三个会计期
    last_year_4_9_12_period_ids = get_last_year_special_month_period_id(time, [4, 9, 12])  # 去年4/9/12月会计期
    last_year_7_12_period_ids = get_last_year_special_month_period_id(time, [7, 12])       # 去年7/12月会计期
    min_period_id = min([last_year_period_id] + last_quarter_3period_ids + last_year_4_9_12_period_ids
                        + last_year_7_12_period_ids)                                       # 需要用到的最早的会计期

    try:
        # AI预测结果
        ai_fcst_df = read_ai_table_from_db(ai_fcst_tbl_name, min_period_id)
        # 集团/产业专家预测结果
        expert_fcst_df_list = read_expert_table_from_db
        # 真实数据
        sql = f"""
        WITH LatestVersion AS (
            SELECT version_code
            FROM {VERSION_TBL_NAME}
            WHERE step = 1
            ORDER BY last_update_date DESC
            LIMIT 1
        ),
        FilteredData AS (
                SELECT period_id, target_period, bg_code, bg_name, 
                oversea_code, oversea_desc, lv1_code, lv1_name, 
                lv2_code, lv2_name, equip_rev_cons_after_amt, mgp_ratio_after,
                RANK() OVER (PARTITION BY target_period ORDER BY period_id DESC) AS rn
            FROM {fact_tbl_name} t1
            WHERE del_flag = 'N'
              AND NOT (target_period LIKE '%YTD')
              AND period_id >= '{min_period_id}'
              AND oversea_desc = '全球'
              AND currency = 'CNY'
              AND version_code IN (SELECT version_code FROM LatestVersion)
        )
        SELECT period_id, target_period, bg_code, bg_name, 
                oversea_code, oversea_desc, lv1_code, lv1_name, 
                lv2_code, lv2_name, equip_rev_cons_after_amt, mgp_ratio_after
        FROM FilteredData
        WHERE rn = 1
        ORDER BY period_id, target_period;
        """
        fact_df = get_data(sql)
        fact_df = fact_df.drop_duplicates()
        for value_col in ['equip_rev_cons_after_amt', 'mgp_ratio_after']:
            fact_df[value_col] = fact_df[value_col].astype(float)
        logger.info(f"读入事实表大小:{fact_df.shape}")
    except Exception as e:
        logger.error(f"读取数据失败，请检查数据库连接和SQL语句:{str(e)}")
        raise
    return ai_fcst_df, expert_fcst_df_list, fact_df


def concat_lv1_lv2_data(df: pd.DataFrame
                        ) -> pd.DataFrame:
    """
    拼接AI预测 /专家预测 / 事实数据表的LV1、LV2层级数据

    Parameters
    ----------
    df
        AI预测 /专家预测 / 事实数据表

    Returns
    -------
    拼接LV1、LV2层级后的数据
    """
    # LV1层级数据
    tmp_df = df[(df['lv1_name'].notnull()) & (df['lv2_name'].isnull())]
    tmp_df["chanye_level"] = "LV1"
    tmp_df["chanye_name"] = tmp_df["lv1_name"]
    tmp_df["chanye_code"] = tmp_df["lv1_code"]
    # LV2层级数据
    tmp_df2 = df[(df['lv1_name'].notnull()) & (df['lv2_name'].notnull())]
    tmp_df2["chanye_level"] = "LV2"
    tmp_df2["chanye_name"] = tmp_df2["lv2_name"]
    tmp_df2["chanye_code"] = tmp_df2["lv2_code"]
    df_ = pd.concat([tmp_df, tmp_df2])
    df_.reset_index(drop=True, inplace=True)

    return df_


def handle_anomaly_data(x, col_name):
    """
    处理专家预测的异常数据: 如果数据异常，将异常结果置空后返回

    Parameters
    ----------
    x
    col_name
        列名

    Returns
    -------

    """
    data = x.to_dict()
    mgp_rate = data[col_name]
    if not mgp_rate or mgp_rate > 1 or mgp_rate < -1:
        return None

    return mgp_rate


def check_value_is_null(x, col_name):
    """
    检查指定列的值是否为空

    Parameters
    ----------
    x
    col_name

    Returns
    -------

    """
    data = x.to_dict()
    is_null = False
    try:
        if data[col_name] is None or str(data[col_name]).strip() == NULL_STRING_PLACE_HOLDER:
            is_null = True
    except Exception as ex:
        is_null = False

    return is_null


def compare_two_column(x, col_name1, col_name2):
    """
    比较DataFrame中两列数值整数部分是否相同(考虑处理误差，使用差值的绝对值小于1做判断)

    Parameters
    ----------
    x
    col_name1
        待比较的列名1
    col_name2
        待比较的列名2

    Returns
    -------

    """
    data = x.to_dict()
    is_equal = False
    try:
        if abs(data[col_name1] - data[col_name2]) < 1.0:
            is_equal = True
    except Exception as ex:
        is_equal = False

    return is_equal


def replace_value(x, target_columns, suffix):
    """根据指定列名带后缀的列是否有值做替换
    """
    data = x.to_dict()
    col_name = target_columns[0] + suffix
    if data.get(col_name) and not pd.isna(data.get(col_name)):  # 带后缀的列值不为空时，取带后缀的列值
        res = tuple([data.get(col + suffix) for col in target_columns])
    else:
        res = tuple([data.get(col) for col in target_columns])
    if len(res) == 1:
        res = res[0]

    return res


def calc_accurary_by_rule(x: pd.Series,
                          hist_merged_df: pd.DataFrame,
                          metric: str,
                          metric_true: str,
                          ):
    """
    根据业务规则计算预测结果准确率

    Parameters
    ----------
    x
        待评估数据
    hist_merged_df
        历史期次数据
    metric
        待评估指标: 制造毛利率, 设备收入

    Returns
    -------
    评估的准确率
    """
    acc = None
    data = x.to_dict()
    if x["target_period_level"] == "year":           # 使用年度预测规则计算准确率
        acc = calc_accurary_by_year(data, metric, metric_true, hist_merged_df)
    elif x["target_period_level"] == "half-a-year":  # 使用半年度预测规则计算准确率
        acc = calc_accurary_by_half_year(data, metric, metric_true, hist_merged_df)
    elif x["target_period_level"] == "quarter":      # 使用季度预测规则计算准确率
        acc = calc_accurary_by_quarter(data, metric, metric_true, hist_merged_df)
    else:
        pass  # 不做评估融合

    return acc


def query_hist_data_in_dataframe(data: Dict,
                                 period_id: str,
                                 target_period: str,
                                 hist_merged_df: pd.DataFrame
                                 ) -> Dict:
    """

    Parameters
    ----------
    data
        待查询数据
    period_id
        当期会计期
    target_period
        预测目标期次
    hist_merged_df
        历史期次数据

    Returns
    -------

    """
    bg_code = data["bg_code"]
    chanye_level = data["chanye_level"]
    chanye_code = data["chanye_code"]
    oversea_desc = data["oversea_desc"]

    query_condition = "period_id == @period_id "\
                      "& target_period == @target_period "\
                      "& bg_code == @bg_code " \
                      "& chanye_level == @chanye_level "\
                      "& chanye_code == @chanye_code "\
                      "& oversea_desc == @oversea_desc "
    res_df = hist_merged_df.query(query_condition)
    if 1 <= res_df.shape[0] <= 2:
        res_dict = res_df.to_dict(orient="list")
        for k, v in res_dict.items():
            res_dict[k] = v[0]

    elif res_df.shape[0] == 0:
        res_dict = {}
    else:
        print(f"ERROR:查询到多条数据:{res_df}")
        res_dict = {}

    return res_dict


def merge_ai_expert_fact_data(df_tuple: Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame],
                              ) -> pd.DataFrame:
    """
    合并各期AI预测结果、专家预测结果和事实结果

    Parameters
    ----------
    df_tuple
        AI预测结果、专家预测结果、事实表结果

    Returns
    -------
    合并后的结果
    """
    ai_fcst_df_, expert_fcst_df_, fact_df_ = df_tuple

    # 取最大的phase_date
    dimension_list = [    # 待融合的维度和指标
        "period_id", "target_period", "bg_code", "bg_name",
        "chanye_level", "chanye_name", "chanye_code",
        "oversea_desc"
    ]
    ai_fcst_df_ = get_max_phase_data(ai_fcst_df_, dimension_list)
    print(f"get max phase_date ai_pred_df size:{ai_fcst_df_.shape}")

    # merge AI预测和专家预测
    expert_merge_cols = [
        "period_id", "target_period", "bg_code",
        "chanye_level", "chanye_name","chanye_code",
        "oversea_desc"
    ]
    suffix = "_.x._"
    hist_merged_df = pd.merge(ai_fcst_df_, expert_fcst_df_, on=expert_merge_cols, how="left", suffixes=("", suffix))
    hist_merged_df.drop(columns=[col for col in hist_merged_df.columns if col.endswith(suffix)], inplace=True)

    # merge事实表结果
    fact_merge_cols = [
        "target_period", "bg_code",
        "chanye_level", "chanye_name","chanye_code",
        "oversea_desc"
    ]
    hist_merged_df = pd.merge(hist_merged_df, fact_df_, on=fact_merge_cols, how="left", suffixes=("", suffix))
    hist_merged_df.drop(columns=[col for col in hist_merged_df.columns if col.endswith(suffix)], inplace=True)

    return hist_merged_df


def dim_expert_preprocess_rule(
        expert_fcst_df_list: List[pd.DataFrame],
        expert_fcst_tbl_names: List[str],
        time: str
):
    # 会计期为1,4,7,10月不应用预处理规则
    if int(time[4:]) in {1, 4, 7, 10}:
        return

    try:
        idx = expert_fcst_tbl_names.index(DIM_EXPERT_FCST_TBL_NAME)
    except ValueError:
        idx = None
    if idx is None:
        return

    # 当期会计期lv1粒度的量纲分析师专家预测结果
    dim_fcst_df = expert_fcst_df_list[idx]
    current_year = time[:4]
    dim_fcst_df_ = dim_fcst_df[
        (dim_fcst_df["period_id"] == time) & (dim_fcst_df["target_period"] == current_year)
        & (dim_fcst_df["lv2_name"] == NULL_STRING_PLACE_HOLDER)
        ]

    if dim_fcst_df_.shape[0] == 0:  # 当期会计期预测结果为空
        # 取上个会计期的专家预测值
        last_period_id = get_last_month_period_id(time)
        last_dim_fcst_df_ = dim_fcst_df[(dim_fcst_df["period_id"] == last_period_id)].copy()
        last_dim_fcst_df_["period_id"] = time
        # 将上个会计期的专家预测结果修改会计期后拼接到专家预测结果中
        dim_fcst_df = pd.concat([dim_fcst_df, last_dim_fcst_df_], ignore_index=True)
        expert_fcst_df_list[idx] = dim_fcst_df


def chanye_expert_preprocess_rule(
    expert_fcst_df_list: List[pd.DataFrame],
    expert_fcst_tbl_names: List[str],
    fact_df: pd.DataFrame,
    time: str
):
    # 会计期为1月时,不应用预处理规则
    if int(time[4:]) in {1}:
        return

    try:
        idx = expert_fcst_tbl_names.index(CHANYE_EXPERT_FCST_TBL_NAME)
    except ValueError:
        idx = None
    if idx is None:
        return

    # 当期会计期lv1粒度的产业专家预测结果
    chanye_fcst_df = expert_fcst_df_list[idx]
    current_year = time[:4]
    chanye_fcst_df_ = chanye_fcst_df[
        (chanye_fcst_df["period_id"] == time) & (chanye_fcst_df["target_code"] == current_year)
        & (chanye_fcst_df["lv2_name"].isnull())
        ]

    # 当期会计期预测结果为空
    if chanye_fcst_df_.shape[0] == 0:
        # 取上个会计期的专家预测值
        last_period_id = get_last_month_period_id(time)
        last_chanye_fcst_df_ = chanye_fcst_df[(chanye_fcst_df["period_id"] == last_period_id)].copy()
        last_chanye_fcst_df_["period_id"] = time
        # 将上个会计期的专家预测结果修改会计期后拼接到专家预测结果中
        chanye_fcst_df = pd.concat([chanye_fcst_df, last_chanye_fcst_df_], ignore_index=True)
        expert_fcst_df_list[idx] = chanye_fcst_df
        return

    # 事实表的年度累计值
    sum_df = fact_df[(fact_df["lv2_name"].isnull()) & (fact_df["target_period"] == current_year)]

    # 比较产业专家预测结果和事实表的年度累计值结果
    merged_df = pd.merge(chanye_fcst_df_, sum_df, on=["bg_name", "lv1_name"], how="left", suffixes=("", "s"))
    merged_df["is_equal"] = merged_df.apply(
        lambda x: compare_two_column(x, EQUIP_REV_AMT_FIELD, FACT_EQUIP_REV_AMT_FIELD),
        axis=1
    )

    if not merged_df["is_equal"].any():
        return

    # 过滤出需要替换的上个会计期的专家预测值并做替换
    mark_df = merged_df[merged_df["is_equal"] == True][["bg_name", "lv1_name"]]
    last_period_id = get_last_month_period_id(time)
    chanye_fcst_df_ = chanye_fcst_df[(chanye_fcst_df["period_id"] == last_period_id)].copy()  # 上个会计期的专家预测值
    chanye_fcst_df_["period_id"] = time
    to_replace_df = pd.merge(chanye_fcst_df_, mark_df, on=["bg_name", "lv1_name"], how="inner", suffixes=("", "t"))
    merged_df = pd.merge(chanye_fcst_df, to_replace_df, on=[
        "period_id", "target_period", "bg_name", "lv1_name", "lv2_name"], how="left", suffixes=("", "t"))
    replace_columns = [EQUIP_REV_AMT_FIELD, MGP_RATE_FIELD]
    replaced_df = merged_df.apply(
        lambda x: replace_value(x, replace_columns, "t"),
        axis=1,
        result_type="expand"
    )
    if len(replace_columns) == 1:
        replaced_df = replaced_df.to_frame()
    replaced_df.columns = replace_columns
    chanye_fcst_df[EQUIP_REV_AMT_FIELD] = replaced_df[EQUIP_REV_AMT_FIELD]
    chanye_fcst_df[MGP_RATE_FIELD] = replaced_df[MGP_RATE_FIELD]
    expert_fcst_df_list[idx] = chanye_fcst_df


def preprocess_expert_fcst_data(
        expert_fcst_df_list: List[pd.DataFrame],
        expert_fcst_tbl_names: List[str],
        fact_df: pd.DataFrame,
        time: str
):
    """
    专家预测结果预处理:
        1.产业分析师预测结果
            如果会计期是1月，不应用后续规则；
            如果预测结果为空（lv1粒度），则使用上一个会计期的预测结果作为当期的预测结果;
            如果预测结果等于事实表累加和（lv1粒度），使用上一个会计期的预测结果作为当期的预测结果;
        2.集团分析师预测结果
            如果会计期是1,4,7,10这四个月份，不应用后续规则；
            如果预测结果为空（lv1粒度），则使用上一个会计期的预测结果作为当期的预测结果;

    备注：
        分析师预测结果为空，指的是不存在相关的记录(行),需要通过和上一个会计期进行对比来判断

    Parameters
    ----------
    expert_fcst_df_list
        专家预测结果列表
    expert_fcst_tbl_names
        专家预测结果表名列表
    fact_df
        事实表
    time
        当前会计期

    Returns
    -------

    """
    # 产业分析师预测结果预处理规则
    chanye_expert_preprocess_rule(expert_fcst_df_list, expert_fcst_tbl_names, fact_df, time)

    # 量纲分析师预测结果预处理规则
    dim_expert_preprocess_rule(expert_fcst_df_list, expert_fcst_tbl_names, time)


def preprocess_data(
        df_tuple: Tuple[pd.DataFrame, List[pd.DataFrame], pd.DataFrame],
        expert_fcst_tbl_names: List[str],
        time: str
) -> Tuple[pd.DataFrame, List[pd.DataFrame], pd.DataFrame]:
    ai_fcst_df, expert_fcst_df_list, fact_df = df_tuple

    # 异常值处理
    ai_fcst_df[AI_MGP_RATE_FIELD] = ai_fcst_df.apply(
        lambda x: handle_anomaly_data(x, AI_MGP_RATE_FIELD),
        axis=1
    )
    for expert_fcst_df in expert_fcst_df_list:
        expert_fcst_df[MGP_RATE_FIELD] = expert_fcst_df.apply(
            lambda x: handle_anomaly_data(x, MGP_RATE_FIELD),
            axis=1
        )
    fact_df[FACT_MGP_RATE_FIELD] = fact_df.apply(
        lambda x: handle_anomaly_data(x, FACT_MGP_RATE_FIELD),
        axis=1
    )

    # 专家预测结果预处理
    preprocess_expert_fcst_data(expert_fcst_df_list, expert_fcst_tbl_names, fact_df, time)

    # 拼接LV1和LV2数据 - level, name, code
    expert_fcst_df_list_ = []
    ai_fcst_df_ = concat_lv1_lv2_data(ai_fcst_df)
    for expert_fcst_df in expert_fcst_df_list:
        expert_fcst_df_ = concat_lv1_lv2_data(expert_fcst_df)
        expert_fcst_df_list_.append(expert_fcst_df_)
    fact_df['lv2_name'] = fact_df['lv2_name'].apply(lambda x: None if x == 'SNULLC' else x)
    fact_df_ = concat_lv1_lv2_data(fact_df)

    # 得到预测时间粒度  - target_period_level: quarter, half-a-year, year
    ai_fcst_df_["target_period_level"] = ai_fcst_df_["target_period"].apply(get_target_period_level)
    for expert_fcst_df_ in expert_fcst_df_list_:
        expert_fcst_df_["target_period_level"] = expert_fcst_df_["target_period"].apply(
            get_target_period_level
        )

    return ai_fcst_df_, expert_fcst_df_list_, fact_df_


def get_max_phase_data(ai_fcst_df: pd.DataFrame,
                       dimension_list: List[str]
                       ) -> pd.DataFrame:
    """
    AI预测结果如果存在多个phase_date(SOP期次), 取最大的期次

    Parameters
    ----------
    ai_fcst_df
        AI预测结果
    dimension_list
        区分不同预测期次的维度列表

    Returns
    -------
    筛选出的最大期次的结果表
    """
    phase_col = "phase_date"
    sort_cols = dimension_list + [phase_col]

    # 将空字符占位符替换回空值符，避免影响排序结果的顺序
    ai_fcst_df_tmp = ai_fcst_df.copy()
    ai_fcst_df_tmp[phase_col] = ai_fcst_df[phase_col].replace(NULL_STRING_PLACE_HOLDER, "")

    max_phase_date = ai_fcst_df_tmp.sort_values(sort_cols).groupby(dimension_list).tail(1)
    ai_fcst_df_ = ai_fcst_df.iloc[max_phase_date.index].copy()
    ai_fcst_df_.reset_index(drop=True, inplace=True)

    return ai_fcst_df_


def calc_accurary(metric: str,
                  metric_true: str,
                  res_dict: Dict
                  ) -> float:
    """
    计算指标准确率
    .. math:: 设备收入评估准确率 = 1 - MAPE = 1 - \\frac{1}{n} \\sum_{i}^{n} \\vert \\frac{实际值 - 预测值}{实际值} \\vert
    .. math:: 制毛率评估准确率 = 1 - MAE = 1-\\frac{1}{n} \\sum_{i}^{n} \\vert 实际值 - 预测值 \\vert

    Parameters
    ----------
    metric
        待评估指标: 制毛率, 设备收入
    res_dict

    Returns
    -------

    """
    acc = None
    if not res_dict.get(metric) or not res_dict.get(metric_true):
        return acc

    try:
        if metric == AI_MGP_RATE_FIELD or metric == MGP_RATE_FIELD:
            acc = 1 - abs(res_dict[metric_true] - res_dict[metric])
        elif metric == AI_EQUIP_REV_AMT_FIELD or EQUIP_REV_AMT_FIELD:
            if res_dict[metric_true] == 0.0:
                acc = 0
            else:
                acc = 1 - np.true_divide(      # MAPE的范围会超过1导致acc为负值,不处理
                    abs(res_dict[metric_true] - res_dict[metric]),
                    abs(res_dict[metric_true])
                )
        if isinstance(acc, float) and np.isnan(acc):
            acc = None
    except Exception as ex:
        msg = f"计算指标{metric}准确率抛出异常:{ex}"
        logging.warning(msg)

    return acc


def calc_last_quarter_accurary(data: Dict,
                               last_quarter_period_ids: List[str],
                               metric: str,
                               metric_true: str,
                               hist_merged_df: pd.DataFrame
                               ) -> float:
    """
    计算上一个季度三个会计期预测上一个季度的平均准确率

    Parameters
    ----------
    data
    last_quarter_period_ids
    metric
    hist_merged_df

    Returns
    -------

    """
    quarter_acc_list = []

    for period_id in last_quarter_period_ids:
        target_period = period_id[:4] + "Q" + str((int(period_id[4:]) - 1) // 3 + 1)
        res_dict = query_hist_data_in_dataframe(data, period_id, target_period, hist_merged_df)
        acc = calc_accurary(metric, metric_true, res_dict)
        if acc is not None:
            quarter_acc_list.append(acc)

    if len(quarter_acc_list) > 0:
        acc = np.mean(quarter_acc_list)
    else:
        acc = None

    return acc


def calc_accurary_by_year(data: Dict,
                          metric: str,
                          metric_true: str,
                          hist_merged_df: pd.DataFrame
                          ) -> float:
    """
    计算年度预测结果的准确率
    .. math:: mean(去年同期年度预测准确率,去年7月年度预测准确率,去年12月年度预测准确率，mean(上一个季度三个会计期预测上一个季度的准确率))

    Parameters
    ----------
    data
        待评估的当期数据(一行数据)
    metric
        待评估指标: 制毛率, 设备收入
    metric_true
        待评估指标的真实值列名
    hist_merged_df

    Returns
    -------

    """
    period_id = data["period_id"]
    last_year_period_ids = [get_last_year_period_id(period_id)] + \
                           get_last_year_special_month_period_id(period_id, [7, 12])  # 去年同期和去年7、12月的会计期
    last_quarter_period_ids = get_last_quarter_3period_id(period_id)                  # 上一个季度的三个会计期

    acc_list = []
    for period_id in last_year_period_ids:
        target_period = period_id[:4]
        res_dict = query_hist_data_in_dataframe(data, period_id, target_period, hist_merged_df)
        acc = calc_accurary(metric, metric_true, res_dict)
        if acc is not None:
            acc_list.append(acc)

    acc = calc_last_quarter_accurary(data, last_quarter_period_ids, metric, metric_true, hist_merged_df)
    if isinstance(acc, float) and not np.isnan(acc):
        acc_list.append(acc)

    if len(acc_list) > 0:
        acc = np.mean(acc_list)
    else:
        acc = None

    return acc


def calc_accurary_by_half_year(data: Dict,
                               metric: str,
                               metric_true: str,
                               hist_merged_df: pd.DataFrame
                               ) -> float:
    """
    计算半年度粒度预测的准确率
    .. math:: mean(去年同期半年度预测准确率, 去年4月预测上半年准确率, 去年9月预测下半年准确率, 去年12月预测下半年准确率,
                   mean(上一个季度三个会计期预测上一个季度的准确率))

    Parameters
    ----------
    data
        待评估的当期数据
    metric
        待评估指标: 制毛率, 设备收入
    hist_merged_df

    Returns
    -------

    """
    period_id = data["period_id"]
    last_half_year_period_ids = [get_last_year_period_id(period_id)] + \
                                get_last_year_special_month_period_id(period_id, [4, 9, 12])  # 去年同期和去年4,9,12月的会计期
    last_quarter_period_ids = get_last_quarter_3period_id(period_id)                          # 上一个季度的三个会计期

    acc_list = []
    for period_id in last_half_year_period_ids:
        target_period = period_id[:4] + "H" + str((int(period_id[4:]) - 1) // 6 + 1)
        res_dict = query_hist_data_in_dataframe(data, period_id, target_period, hist_merged_df)
        acc = calc_accurary(metric, metric_true, res_dict)
        if isinstance(acc, float):
            acc_list.append(acc)

    acc = calc_last_quarter_accurary(data, last_quarter_period_ids, metric, metric_true, hist_merged_df)
    if isinstance(acc, float):
        acc_list.append(acc)

    if len(acc_list) > 0:
        acc = np.mean(acc_list)
    else:
        acc = None

    return acc


def calc_accurary_by_quarter(data: Dict,
                             metric: str,
                             metric_true: str,
                             hist_merged_df: pd.DataFrame
                             ) -> float:
    """
    计算季度粒度预测的准确率
    ..math:: mean(去年同期季度预测准确率, mean(上一个季度三个会计期预测上一个季度准确率))

    Parameters
    ----------
    data
        待评估的当期数据
    metric
        待评估指标: 制毛率, 设备收入
    hist_merged_df

    Returns
    -------
    """
    period_id = data["period_id"]
    last_year_period_id = get_last_year_period_id(period_id)          # 去年同期的会计期
    last_quarter_period_ids = get_last_quarter_3period_id(period_id)  # 上一个季度的三个会计期

    acc_list = []
    quarter = str((int(last_year_period_id[4:]) - 1) // 3 + 1)
    target_period = last_year_period_id[:4] + "Q" + quarter
    res_dict = query_hist_data_in_dataframe(data, last_year_period_id, target_period, hist_merged_df)
    acc = calc_accurary(metric, metric_true, res_dict)
    if acc is not None:
        acc_list.append(acc)

    acc = calc_last_quarter_accurary(data, last_quarter_period_ids, metric, metric_true, hist_merged_df)
    if acc is not None:
        acc_list.append(acc)

    if len(acc_list) > 0:
        acc = np.mean(acc_list)
    else:
        acc = None

    return acc


def calc_weight(x: pd.Series,
                metric: str
                ) -> float:
    """
    根据已经计算出AI预测结果的权重，计算待融合的专家预测结果的权重

    Parameters
    ----------
    x
    metric

    Returns
    -------

    """
    data = x.to_dict()
    ai_acc_col = f"ai_acc_{metric}"
    expert_acc_col = f"expert_acc_{metric}"
    ai_weight_col = f"ai_weight_{metric}"
    ai_weight = data[ai_weight_col]
    ai_acc = data[ai_acc_col]
    expert_acc = data[expert_acc_col]

    weight = None
    if ai_acc is None and expert_acc:
        weight = 1.0
    elif ai_acc and expert_acc:
        weight = 1 - ai_weight

    return weight


def calc_weight_by_softmax(x: pd.Series,
                           metric: str,
                           data_type: str
                           ) -> float:
    """
    根据评估准确率计算融合权重
    .. math:: mape_i = 1 - acc_i
    .. math:: v_i &= log \\frac{1}{{mape}_i}
    .. math:: {weight}_i &= softmax(v_i) = \\frac{e^{v_i}}{\\sum_{j}^{n}e^{v_j}}

    Parameters
    ----------
    x
        待融合数据
    metric
        待融合指标: 制造毛利率, 设备收入
    data_type
        数据类型: "ai", "expert"

    Returns
    -------
    融合权重
    """
    # 准确率列名

    weight = None
    data = x.to_dict()
    ai_acc_col = f"ai_acc_{metric}"
    expert_acc_col = f"expert_acc_{metric}"
    if data.get(ai_acc_col) and data.get(expert_acc_col):
        def metric_val(acc):
            return np.log(np.true_divide(1, 1 - acc))

        col_name = f"{data_type}_acc_{metric}"
        weight = np.true_divide(
            np.exp(metric_val(data[col_name])),
            np.sum([np.exp(metric_val(data[ai_acc_col])), np.exp(metric_val(data[expert_acc_col]))])
        )

    return weight


def calc_combined_weights(hist_merged_df_dict: Dict[int, Tuple[pd.DataFrame, pd.DataFrame]],
                          ai_fcst_metric_list: List[str],
                          expert_fcst_metric_list: List[str],
                          fact_metric_list: List[str]
                          ):
    """
    根据规则计算预测准确率，再计算融合权重

    Parameters
    ----------
    hist_merged_df_dict
        不同类型专家的merge结果
    ai_fcst_metric_list
        评估指标在AI预测结果中的列名
    expert_fcst_metric_list
        评估指标在专家预测结果中的列名
    fact_metric_list:
        评估指标在事实表中的列名

    Returns
    -------
    无返回值，融合权重结果在DataFrame的新增列 ai_weight_{metric},expert_weight_{metric} 中
    """
    # 计算AI预测的准确率
    for k, (hist_merged_df, merged_df) in hist_merged_df_dict.items():
        merged_df_list = []
        merged_df_ = merged_df.copy()
        hist_merged_df_ = hist_merged_df
        for idx, metric in enumerate(ai_fcst_metric_list):
            metric_true = fact_metric_list[idx]
            col_name = f"ai_acc_{metric}"
            merged_df_[col_name] = merged_df_.apply(
                lambda x: calc_accurary_by_rule(x, hist_merged_df_, metric, metric_true),
                axis=1
            )
        merged_df_list.append(merged_df_)
        merged_df = pd.concat(merged_df_list, axis=0)
        hist_merged_df_dict[k] = hist_merged_df, merged_df  # 将结果更新到hist_merged_df_dict中

    # 根据规则计算专家预测结果的准确率
    for _, merged_df_tuple in hist_merged_df_dict.items():
        hist_merged_df, merged_df = merged_df_tuple
        for idx, metric in enumerate(expert_fcst_metric_list):
            metric_true = fact_metric_list[idx]
            ai_metric = ai_fcst_metric_list[idx]
            col_name = f"expert_acc_{ai_metric}"
            merged_df[col_name] = merged_df.apply(
                lambda x: calc_accurary_by_rule(x, hist_merged_df, metric, metric_true),
                axis=1
            )

    # 计算融合权重
    for k, (hist_merged_df, merged_df) in hist_merged_df_dict.items():
        merged_df_list = []
        merged_df_ = merged_df.copy()
        hist_merged_df_ = hist_merged_df
        for metric in ai_fcst_metric_list:
            # 计算AI预测结果融合权重
            ai_col_name = f"ai_weight_{metric}"
            merged_df_[ai_col_name] = merged_df_.apply(
                lambda x: calc_weight_by_softmax(x, metric, "ai"),
                axis=1
            )
            # 计算专家预测结果融合权重
            expert_col_name = f"expert_weight_{metric}"
            merged_df_[expert_col_name] = merged_df_.apply(
                lambda x: calc_weight(x, metric),
                axis=1
            )
        merged_df_list.append(merged_df_)
        merged_df = pd.concat(merged_df_list, axis=0)
        hist_merged_df_dict[k] = hist_merged_df, merged_df  # 将结果更新到hist_merged_df_dict中


def calc_combined_result(hist_merged_df_dict: Dict[int, Tuple[pd.DataFrame, pd.DataFrame]],
                         ai_fcst_metric_list: List[str],
                         expert_fcst_metric_list: List[str],
                         expert_fcst_type_dict: Dict[int, str]
                         ) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    根据融合权重计算融合后的预测结果

    Parameters
    ----------
    hist_merged_df_dict
    ai_fcst_metric_list
    expert_fcst_metric_list
    expert_fcst_type_dict

    Returns
    -------

    """
    hist_merged_df_list = []
    combined_df_list = []
    for expert_idx, (hist_merged_df, merged_df) in hist_merged_df_dict.items():
        for idx, ai_metric in enumerate(ai_fcst_metric_list):
            expert_metric = expert_fcst_metric_list[idx]
            col_name = f"combined_{ai_metric}"
            merged_df[col_name] = merged_df.apply(
                lambda x: combined_fcst_by_weight(x, ai_metric, expert_metric),
                axis=1
            )
        expert_fcst_type = expert_fcst_type_dict[expert_idx]
        merged_df["combined_expert"] = expert_fcst_type
        hist_merged_df["combined_expert"] = expert_fcst_type

        combined_df = merged_df.rename(columns={
            ai_fcst_metric_list[0]: f"raw_{ai_fcst_metric_list[0]}",
            ai_fcst_metric_list[1]: f"raw_{ai_fcst_metric_list[1]}",
        })
        combined_df = combined_df.rename(columns={
            f"combined_{ai_fcst_metric_list[0]}": ai_fcst_metric_list[0],
            f"combined_{ai_fcst_metric_list[1]}": ai_fcst_metric_list[1],
        })
        hist_merged_df_list.append(hist_merged_df)
        combined_df_list.append(combined_df)

    # 拼接数据
    hist_merged_df = pd.concat(hist_merged_df_list, axis=0)
    combined_df = pd.concat(combined_df_list, axis=0)
    hist_merged_df.reset_index(drop=True, inplace=True)
    combined_df.reset_index(drop=True, inplace=True)

    # 空值填充
    combined_df[AI_MGP_RATE_FIELD].fillna(99999, inplace=True)
    combined_df[AI_EQUIP_REV_AMT_FIELD].fillna(0.0, inplace=True)

    # 将空字符串替换为占位符
    combined_df["lv2_code"].fillna(NULL_STRING_PLACE_HOLDER, inplace=True)
    combined_df["lv2_name"].fillna(NULL_STRING_PLACE_HOLDER, inplace=True)
    combined_df["phase_date"].fillna(NULL_STRING_PLACE_HOLDER, inplace=True)

    combined_df["lv2_code"].replace("", NULL_STRING_PLACE_HOLDER, inplace=True)
    combined_df["lv2_name"].replace("", NULL_STRING_PLACE_HOLDER, inplace=True)
    combined_df["phase_date"].replace("", NULL_STRING_PLACE_HOLDER, inplace=True)

    return combined_df, hist_merged_df


def combined_fcst_by_weight(x: pd.Series,
                            ai_metric: str,
                            expert_metric: str
                            ) -> float:
    """
    根据融合权重计算AI预测融合专家预测的结果

    Parameters
    ----------
    x
        待融合数据
    ai_metric
        待融合指标: 制造毛利率, 设备收入
    expert_metric
        待融合指标: 制造毛利率, 设备收入(专家预测结果表中的列名)

    Returns
    -------
    融合后的预测结果
    """
    ai_weight_col = f"ai_weight_{ai_metric}"
    expert_weight_col = f"expert_weight_{ai_metric}"

    data = x.to_dict()
    if data.get(expert_metric) is None or np.isnan(data[expert_metric]):  # 专家预测结果为空
        combined_val = data[ai_metric]
    elif data.get(ai_metric) is None or np.isnan(data[ai_metric]):        # AI预测结果为空，专家预测结果不为空
        combined_val = data[expert_metric]
    elif data.get(expert_weight_col) is None or \
            data.get(ai_weight_col) is None:                              # AI预测结果或专家预测结果权重为空
        combined_val = data[ai_metric]
    else:                                                                 # AI预测和专家预测都不为空
        combined_val = sum([
            data[ai_weight_col] * data[ai_metric],
            data[expert_weight_col] * data[expert_metric]
        ])

    return combined_val


def combine_expert_result(
        db_table_names: Tuple[str, List[str], str],
        time: str,
        run_env: str,
        expert_data_type: str
) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    融合AI预测和专家预测结果

    Parameters
    ----------
    db_table_names
        AI预测结果表名,量纲、产业专家预测结果表名,真实数据表
    time
        当前会计期
    run_env
        当前运行环境: sit, uat, prod
    expert_data_type
        专家预测的数据类型: group, chanye, both

    Returns
    -------
    AI预测和专家预测融合的中间数据和融合后的结果
    """
    ai_fcst_tbl_name, expert_fcst_tbl_names, fact_tbl_name = db_table_names
    # 从数据库读取数据
    ai_fcst_df, expert_fcst_df_list, fact_df = read_to_combine_data_from_db(
        ai_fcst_tbl_name, expert_fcst_tbl_names, fact_tbl_name, time, run_env)

    # 专家预测结果预处理
    all_df = pd.concat([ai_fcst_df, fact_df] + expert_fcst_df_list, ignore_index=True)
    # 统一转换 period_id 列为字符串类型
    all_df["period_id"] = all_df["period_id"].astype(str)
    for idx, expert_fcst_df in enumerate(expert_fcst_df_list):
        if idx == 0:  # dim
            expert_fcst_df['target_period'] = expert_fcst_df.apply(lambda row: add_target_period(row['period_id'], row['target_code']), axis=1)
        else:  # chanye
            expert_fcst_df['target_period'] = expert_fcst_df['target_code']

    # 处理专家预测结果异常数据
    df_tuple = (ai_fcst_df, expert_fcst_df_list, fact_df)
    ai_fcst_df, expert_fcst_df_list, fact_df = preprocess_data(df_tuple, expert_fcst_tbl_names, time)

    # 过滤预测目标期次为月度的数据,事实表取数的时候就没有月度的数据了
    hist_merged_df_dict = {}
    ai_fcst_df = ai_fcst_df[(ai_fcst_df["target_period_level"] != "month") & (ai_fcst_df["target_period_level"].notnull())]
    ai_fcst_df.reset_index(drop=True, inplace=True)
    for idx, expert_fcst_df in enumerate(expert_fcst_df_list):
        if expert_fcst_df is not None:
            expert_fcst_df = expert_fcst_df[(expert_fcst_df["target_period_level"] != "month") &
                (expert_fcst_df["target_period_level"].notnull())]
            expert_fcst_df.reset_index(drop=True, inplace=True)
            expert_fcst_df_list[idx] = expert_fcst_df

    expert_fcst_type_dict = {0: DIM_EXPERT_MARK, 1: CHANYE_EXPERT_MARK}  # idx: expert_type
    if expert_data_type == DIM_EXPERT_TYPE:
        expert_fcst_df_list.append(None)
    elif expert_data_type == CHANYE_EXPERT_TYPE:
        expert_fcst_df_list.insert(0, None)

    # 处理专家预测结果异常数据，拼接AI预测、专家预测和事实结果表
    for idx, expert_fcst_df in enumerate(expert_fcst_df_list):
        if expert_fcst_df is None:
            continue
        df_tuple = (ai_fcst_df, expert_fcst_df, fact_df)
        hist_merged_df = merge_ai_expert_fact_data(df_tuple)                    # 历史各会计期的merge结果
        merged_df = hist_merged_df[hist_merged_df["period_id"] == time].copy()  # 当期的merge结果
        hist_merged_df_dict[idx] = (hist_merged_df, merged_df)
        logger.info(f"拼接历史AI预测、专家预测({expert_fcst_type_dict.get(idx)})和事实结果表大小:{hist_merged_df.shape}")
        logger.info(f"拼接当期AI预测、专家预测({expert_fcst_type_dict.get(idx)})和事实结果表大小:{merged_df.shape}")

    # 计算AI预测结果和专家预测结果的融合权重
    ai_fcst_metric_list = [AI_MGP_RATE_FIELD, AI_EQUIP_REV_AMT_FIELD]  # 待评估指标在AI预测结果中的列名
    expert_fcst_metric_list = [MGP_RATE_FIELD, EQUIP_REV_AMT_FIELD]    # 待评估指标在专家预测结果中的列名
    fact_metric_list = [FACT_MGP_RATE_FIELD, FACT_EQUIP_REV_AMT_FIELD]  # 待评估指标在事实表结中的列名
    calc_combined_weights(hist_merged_df_dict, ai_fcst_metric_list, expert_fcst_metric_list, fact_metric_list)

    # 计算融合预测结果
    merge_col_list = ["period_id", "target_period", "bg_code", "bg_name", "chanye_level", "chanye_name", "chanye_code",
        "oversea_desc"]
    suffix = "_.x._"
    ai_fcst_df_ = ai_fcst_df[ai_fcst_df["period_id"] == time].copy()  # 当期数据
    for idx, (_, merged_df) in hist_merged_df_dict.items():  # 将融合权重和包含全部phase_date的ai预测结果merge
        merged_df_ = pd.merge(ai_fcst_df_, merged_df, on=merge_col_list, how="left", suffixes=("", suffix))
        merged_df_.drop(columns=[col for col in merged_df_.columns if col.endswith(suffix)], inplace=True)
        hist_merged_df_dict[idx] = (_, merged_df_)

    combined_df, hist_merged_df = calc_combined_result(
        hist_merged_df_dict, ai_fcst_metric_list, expert_fcst_metric_list, expert_fcst_type_dict)

    return hist_merged_df, combined_df
