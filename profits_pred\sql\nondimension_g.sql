 select
 version_code,
 scenarios,
 time_window_code,
 period_id,
 target_period,
 bg_code,
 bg_name,
 oversea_code,
 oversea_desc,
 lv1_code,
 lv1_name,
 lv2_code,
 lv2_name,
 dimension_group_code,
 dimension_group_cn_name,
 dimension_group_en_name,
 dimension_subcategory_code,
 dimension_subcategory_cn_name,
 dimension_subcategory_en_name,
 currency,
 equip_rev_cons_before_amt,
 equip_cost_cons_before_amt,
 ship_qty,
 rev_qty,
 unit_cost,
 unit_price,
 rev_percent,
 carryover_rate,
 mgp_ratio,
 unit_cost_ytd_data,
 unit_price_ytd_data,
 rev_percent_ytd_data,
 carryover_rate_ytd_data,
 mgp_ratio_ytd_data,
 remark,
 created_by,
 creation_date,
 last_updated_by,
 last_update_date,
 del_flag
    from
    dm_fop_dimension_tgt_period_filled_t
    where
    left (target_period,6) < {pred_version}
    and del_flag = 'N'
    and currency = 'CNY'
    and bg_name in ('运营商','政企')
     and version_code = (select version_code
     from fin_dm_opt_fop.dm_fop_dimension_version_t
     where step = 1
     order by last_update_date desc
     limit 1)
    and oversea_desc in ('国内','海外','全球')