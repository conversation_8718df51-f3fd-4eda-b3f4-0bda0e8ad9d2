
from fastapi import Request, status
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError
from slowapi.errors import RateLimitExceeded
from slowapi.middleware import SlowAPIMiddleware
from starlette.middleware.gzip import GZipMiddleware

from profits_pred.pyxis.utils.app_factory import create_app
from app import router as profits_router

from profits_pred.src.utils import middleware
from profits_pred.src.utils.resource_loader import CONFIG, LIMITER, RDB_POOLS
from profits_pred.src.utils.response_generator import response_generator

app = create_app(
    infra=CONFIG.deploy.infra,
    enable_doc=CONFIG.deploy.enable_doc,
    service_name=CONFIG.deploy.service_name,
    uri_prefix=CONFIG.deploy.uri_prefix
)


# 添加启动、关闭处理函数，释放资源
def startup():
    for pool in RDB_POOLS.values():
        pool.reflect()


def shutdown():
    for pool in RDB_POOLS.values():
        pool.release()


app.add_event_handler("startup", startup)
app.add_event_handler("shutdown", shutdown)

# 添加限流器
app.state.limiter = LIMITER

# 挂载子服务
app.include_router(profits_router)

# 添加中间件函数、异常处理函数
app.add_exception_handler(RequestValidationError, middleware.validation_error_handler)
app.add_exception_handler(ValidationError, middleware.validation_error_handler)
app.add_exception_handler(RateLimitExceeded, middleware.rate_limit_exceeded_handler)
app.add_middleware(GZipMiddleware, minimum_size=1000)
# TODO app.add_middleware(middleware.AuthenticationMiddleware)
# TODO app.add_middleware(middleware.RequestMiddleware)
app.add_middleware(SlowAPIMiddleware)
app.add_middleware(middleware.ExceptionMiddleware)
app.add_middleware(middleware.TimingMiddleware)
app.add_middleware(middleware.AuditLoggingMiddleware)


@app.get(f"{CONFIG.deploy.service_name}/health", include_in_schema=False)
def health_check(
        request: Request,
):
    """
    健康检测
    """
    return response_generator(
        status.HTTP_200_OK, middleware.AppBizStatus.SUCCESS, middleware.AppBizStatusMsg.HEALTH_TEST, list()
    )


# nodimension_g和lv2_nodim中的参数后处理异常处理
# pred_version传参动态获取steps新增