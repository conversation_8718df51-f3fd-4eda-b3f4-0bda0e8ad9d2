from enum import Enum
from typing import Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field


class CloudEnum(str, Enum):
    HIS = "HIS"


class InfraEnum(str, Enum):
    AIF = "AIF"
    MA = "MA"
    ADS = "ADS"
    LOCAL = "LOCAL"


class LogLevelEnum(str, Enum):
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARN = "WARN"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class HisConfigServer(BaseModel):
    url: str = Field(..., description="HIS配置中心获取所有配置对应URL")
    app_id: str = Field(..., description="应用ID，用于获取配置")
    du: str = Field(..., description="部署单元名字，用于获取配置")
    environment: str = Field(..., description="部署环境，用于获取配置")
    region: str = Field(..., description="部署区域，用于获取配置")
    version: Optional[str] = Field(default="1.0", description="用于获取配置，默认1.0")
    config_parts: List[str] = Field(..., min_length=2, max_length=2, description="HIS静态Token的J2C加解密用")


class HisApiGatewayAuthorization(BaseModel):
    url: str = Field(..., description="HIS网关获取动态token URL")


class HisApiGatewayAuthentication(BaseModel):
    sgov_token: str = Field(..., description="HIS sGov鉴权动态token URL")
    sgov_uri: str = Field(..., description="HIS sGov鉴权是否订阅URL")


class HisApiGateway(BaseModel):
    authorization: HisApiGatewayAuthorization = Field(..., description="API网关授权相关配置")
    authentication: HisApiGatewayAuthentication = Field(..., description="API网关鉴权相关配置")


class HisDeployConfig(BaseModel):
    cloud: Optional[CloudEnum] = Field(default=CloudEnum.HIS, description="部署云平台")
    infra: Optional[InfraEnum] = Field(default=InfraEnum.LOCAL, description="部署基础设施")
    enable_doc: Optional[bool] = Field(default=True, description="是否开启openapi文档，生产环境建议关闭")
    service_name: Optional[str] = Field(
        default=str(), description="服务上下文根，由于AIF当前路由转发规则特殊，使用AIF部署时此项填空字符串，其他基础设施部署时和uri_prefix字段内容保持一致"
    )
    uri_prefix: Optional[str] = Field(default=str(), description="用于鉴权订阅URI，以及AIF展示openapi文档")
    config_server: HisConfigServer = Field(..., description="配置中心相关")
    api_gateway: HisApiGateway = Field(..., description="API网关")

    model_config = ConfigDict(use_enum_values=True)


class LoggerConfig(BaseModel):
    name: Optional[str] = Field(default="Logger")
    output_directory: Optional[str] = Field(default=None)
    log_to_file: Optional[bool] = Field(default=False)
    log_format: Optional[str] = Field(
        default="%(asctime)s | PID-%(process)d-%(threadName)s-%(thread)d | %(name)s | %(filename)s:%(lineno)d | %("
                "levelname)s | %(message)s"
    )
    level: Optional[LogLevelEnum] = Field(default=LogLevelEnum.INFO)
    max_file_size: Optional[int] = Field(default=1024 * 1024 * 10, gt=0)
    backup_count: Optional[int] = Field(default=5, ge=1)

    model_config = ConfigDict(use_enum_values=True)


class RdbConfig(BaseModel):
    datasource_name: str
    tables: Optional[List[str]] = Field(default=list(), description="要使用的表，ORM模式需指定，用于生成数据表对象")
    echo: Optional[bool] = Field(default=False, description="是否打印连接池日志")
    rdb_schema: Optional[str] = Field(default=None, alias="schema")


class ObsConfig(BaseModel):
    bucket: str
    pool_size: Optional[int] = Field(default=5, ge=1)
    max_overflow: Optional[int] = Field(default=5, ge=0)
    pool_timeout: Optional[float] = Field(default=2, gt=0)
    pool_recycle: Optional[float] = Field(default=60 * 60, gt=0)
    pool_pre_ping: Optional[bool] = Field(default=True)


class LimiterStrategyEnum(str, Enum):
    MOVING_WINDOW = "moving-window"
    FIXED_WINDOW = "fixed-window"
    FIXED_WINDOW_ELASTIC_EXPIRY = "fixed-window-elastic-expiry"


class LimiterUnitEnum(str, Enum):
    YEAR = "year"
    MONTH = "month"
    DAY = "day"
    HOUR = "hour"
    MINUTE = "minute"
    SECOND = "second"


class LimiterConfig(BaseModel):
    storage_uri: Optional[str] = Field(None, description="分布式限流器存储连接串，单机版不填此项")
    strategy: Optional[LimiterStrategyEnum] = Field(LimiterStrategyEnum.MOVING_WINDOW.value, description="限流策略")
    unit: LimiterUnitEnum = Field(..., description="限流时间单位")
    frequency: int = Field(..., ge=1, description="限流频率")

    model_config = ConfigDict(use_enum_values=True)


class Config(BaseModel):
    deploy: HisDeployConfig = Field(..., description="部署平台配置")
    logger: LoggerConfig = Field(..., description="日志输出配置")
    rdb: Optional[Dict[str, RdbConfig]] = Field(default=dict(), description="关系型数据库配置")
    obs: Optional[Dict[str, ObsConfig]] = Field(default=dict(), description="OBS/S3对象存储配置")
    limiter: Optional[LimiterConfig] = Field(dict(), description="Web服务限流器配置")

    model_config = ConfigDict(alias_generator=lambda x: x.upper(), extra="allow")
