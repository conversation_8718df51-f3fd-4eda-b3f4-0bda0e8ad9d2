import math
import os
import pathlib
from unittest import TestCase

import pandas as pd
import pytest

from profitforecast.utils.combine_data import (
    read_to_combine_data_from_db,
    concat_lv1_lv2_data,
    handle_anomaly_data,
    calc_accurary_by_rule,
    query_hist_data_in_dataframe,
    merge_ai_expert_fact_data,
    calc_accurary,
    calc_last_quarter_accurary,
    calc_accurary_by_year,
    calc_accurary_by_half_year,
    calc_accurary_by_quarter,
    calc_weight_by_softmax,
    calc_combined_weights,
    calc_combined_result,
    combined_fcst_by_weight,
    combine_expert_result, calc_weight, get_max_phase_data, preprocess_expert_fcst_data
)
from profitforecast.utils.default_conf import (
    FACT_TBL_NAME,
    AI_FCST_TBL_NAME,
    MGP_RATE_FIELD,
    AI_MGP_RATE_FIELD,
    GROUP_EXPERT_FCST_TBL_NAME,
    CHANYE_EXPERT_FCST_TBL_NAME,
    AI_EQUIP_REV_AMT_FIELD,
    FACT_MGP_RATE_FIELD,
    FACT_EQUIP_REV_AMT_FIELD,
    EQUIP_REV_AMT_FIELD,
    GROUP_EXPERT_TYPE,
    CHANYE_EXPERT_TYPE, NULL_STRING_PLACE_HOLDER
)

case = TestCase()


@pytest.mark.skip  # 数据库有防火墙无法连接,跳过执行
def test_read_to_combine_data_from_db():
    period_id = "202403"
    run_env = "uat"
    ai_df, (expert_df1, expert_df2), fact_df = read_to_combine_data_from_db(
        AI_FCST_TBL_NAME,
        [GROUP_EXPERT_FCST_TBL_NAME, CHANYE_EXPERT_FCST_TBL_NAME],
        FACT_TBL_NAME,
        period_id,
        run_env
    )
    print(f"ai_df size:{ai_df.shape}")
    print(f"group expert_df size:{expert_df1.shape}")
    print(f"chanye expert_df size:{expert_df2.shape}")
    print(f"fact_df size:{fact_df.shape}")
    case.assertGreater(ai_df.shape[0], 0)
    case.assertGreater(expert_df1.shape[0], 0)
    case.assertGreater(expert_df2.shape[0], 0)
    case.assertGreater(fact_df.shape[0], 0)


def test_concat_lv1_lv2_data():
    df = pd.DataFrame({
        "lv1_code": ["code_1_1"] * 8 + ["code_1_2"] * 8,
        "lv1_name": ["name_1_1"] * 8 + ["name_1_2"] * 8,
        "lv2_code": [NULL_STRING_PLACE_HOLDER] * 4 + ["code_2_1"] * 4
                    + [NULL_STRING_PLACE_HOLDER] * 4 + ["code_2_2"] * 4,
        "lv2_name": [NULL_STRING_PLACE_HOLDER] * 4 + ["name_2_1"] * 4
                    + [NULL_STRING_PLACE_HOLDER] * 4 + ["name_2_2"] * 4,
        "data": list(range(8)) + list(range(8))
    })
    concat_df = concat_lv1_lv2_data(df)
    print(concat_df)
    case.assertEqual(concat_df[concat_df["chanye_level"] == "LV1"].shape[0], 8)
    case.assertEqual(concat_df[concat_df["chanye_level"] == "LV2"].shape[0], 8)


@pytest.mark.parametrize("col_name, anomaly_num", [
    (MGP_RATE_FIELD, 2),
    (AI_MGP_RATE_FIELD, 1),
])
def test_handle_anomaly_data(col_name, anomaly_num):
    df = pd.DataFrame({
        MGP_RATE_FIELD: [0.5, 99999, -1.1, 0.6, 0.9],
        AI_MGP_RATE_FIELD: [0, 99999, 0.8, 0.7, 0.8],
    })
    new_col = "new_" + col_name
    df[new_col] = df.apply(
        lambda x: handle_anomaly_data(x, col_name),
        axis=1
    )
    case.assertEqual(sum(df[new_col].isna()), anomaly_num)


def test_get_max_phase_data():
    dimension_list = ["a", "b", "c", "d"]
    ai_df = pd.DataFrame({
        "a": ["a1"] * 12,
        "b": ["b1"] * 6 + ["b2"] * 6,
        "c": ["c1"] * 4 + ["c2"] * 4 + ["c3"] * 4,
        "d": ["d1"] * 3 + ["d2"] * 3 + ["d3"] * 3 + ["d4"] * 3,
        "phase_date": [
            "1-202402", "20240218", "20240102",
            NULL_STRING_PLACE_HOLDER,
            "20231105", "20231225",
            "20231225", "1-202402",
            "1-202312",
            "20231225", "1-202402", NULL_STRING_PLACE_HOLDER,
        ],
        "val": [1.0] * 12,
    })
    max_phase_df = get_max_phase_data(ai_df, dimension_list)
    print(ai_df)
    print(max_phase_df)
    case.assertEqual(6, max_phase_df.shape[0])
    case.assertEqual("20240218", max_phase_df["phase_date"][0])
    case.assertEqual(NULL_STRING_PLACE_HOLDER, max_phase_df["phase_date"][1])
    case.assertEqual("20231225", max_phase_df["phase_date"][2])
    case.assertEqual("20231225", max_phase_df["phase_date"][3])
    case.assertEqual("1-202312", max_phase_df["phase_date"][4])
    case.assertEqual("20231225", max_phase_df["phase_date"][5])


@pytest.mark.parametrize("period_id", ["202401", "202402", "202403"])
@pytest.mark.parametrize("metric, metric_true, expected_acc", [
    (MGP_RATE_FIELD, FACT_MGP_RATE_FIELD, 0.9),
    (EQUIP_REV_AMT_FIELD, FACT_EQUIP_REV_AMT_FIELD, 0.8),
    (AI_MGP_RATE_FIELD, FACT_MGP_RATE_FIELD, 0.9),
    (AI_EQUIP_REV_AMT_FIELD, FACT_EQUIP_REV_AMT_FIELD, 0.8)
])
def test_calc_last_quarter_accurary(period_id, metric, metric_true, expected_acc):
    last_quarter_period_ids = ["202310", "202311", "202312"]
    bg_code = "bg_code"
    bg_name = "bg_name"
    chanye_level = "LV1"
    chanye_code = "chanye_code"
    currency = "CNY"
    oversea_desc = "全球"
    data = {
        "bg_code": bg_code,
        "bg_name": bg_name,
        "chanye_level": chanye_level,
        "chanye_code": chanye_code,
        "oversea_desc": oversea_desc,
        "currency": currency,
    }
    hist_df = pd.DataFrame({
        "period_id": ["202310", "202311", "202312"],
        "target_period": ["2023Q4", "2023Q4", "2023Q4"],
        "bg_code": [bg_code] * 3,
        "bg_name": [bg_name] * 3,
        "chanye_level": [chanye_level] * 3,
        "chanye_code": [chanye_code] * 3,
        "oversea_desc": [oversea_desc] * 3,
        "currency": [currency] * 3,
        AI_MGP_RATE_FIELD: [0.7, 0.9, 0.7],
        AI_EQUIP_REV_AMT_FIELD: [120, 120, 80],
        MGP_RATE_FIELD: [0.7, 0.9, 0.7],
        EQUIP_REV_AMT_FIELD: [120, 120, 80],
        FACT_MGP_RATE_FIELD: [0.8, 0.8, 0.8],
        FACT_EQUIP_REV_AMT_FIELD: [100, 100, 100],
    })
    acc = calc_last_quarter_accurary(data, last_quarter_period_ids, metric, metric_true, hist_df)
    case.assertEqual(round(expected_acc, 2), round(acc, 2))


@pytest.mark.parametrize("metric, metric_true, expected_acc", [
    (AI_MGP_RATE_FIELD, FACT_MGP_RATE_FIELD, 0.9),
    (AI_EQUIP_REV_AMT_FIELD, FACT_EQUIP_REV_AMT_FIELD, 0.8),
    (MGP_RATE_FIELD, FACT_MGP_RATE_FIELD, 0.9),
    (EQUIP_REV_AMT_FIELD, FACT_EQUIP_REV_AMT_FIELD, 0.8)
])
@pytest.mark.parametrize("period_id", ["202401", "202402", "202403"])
def test_calc_accurary_by_year(period_id, metric, metric_true, expected_acc):
    chanye_level = "LV1"
    chanye_code = "chanye_code"
    bg_code = "bg_code"
    bg_name = "bg_name"
    currency = "CNY"
    oversea_desc = "全球"
    data = {
        "period_id": period_id,
        "bg_code": bg_code,
        "bg_name": bg_name,
        "chanye_level": chanye_level,
        "chanye_code": chanye_code,
        "oversea_desc": oversea_desc,
        "currency": currency,
    }
    num = 5
    hist_df = pd.DataFrame({
        "period_id": ["202301", "202302", "202303", "202312", "202307"],
        "target_period": ["2023"] * num,
        "chanye_level": [chanye_level] * num,
        "chanye_code": [chanye_code] * num,
        "bg_code": [bg_code] * num,
        "bg_name": [bg_name] * num,
        "currency": [currency] * num,
        "oversea_desc": [oversea_desc] * num,
        AI_MGP_RATE_FIELD: [0.7, 0.9, 0.7, 0.9, 0.7],
        AI_EQUIP_REV_AMT_FIELD: [120, 120, 80, 120, 80],
        MGP_RATE_FIELD: [0.7, 0.9, 0.7, 0.9, 0.7],
        EQUIP_REV_AMT_FIELD: [120, 120, 80, 120, 80],
        FACT_MGP_RATE_FIELD: [0.8] * num,
        FACT_EQUIP_REV_AMT_FIELD: [100] * num,
    })
    acc = calc_accurary_by_year(data, metric, metric_true, hist_df)
    case.assertEqual(round(acc, 2), round(expected_acc, 2))


@pytest.mark.parametrize("metric, metric_true, expected_acc", [
    (MGP_RATE_FIELD, FACT_MGP_RATE_FIELD, 0.9),
    (EQUIP_REV_AMT_FIELD, FACT_EQUIP_REV_AMT_FIELD, 0.8),
    (AI_MGP_RATE_FIELD, FACT_MGP_RATE_FIELD, 0.9),
    (AI_EQUIP_REV_AMT_FIELD, FACT_EQUIP_REV_AMT_FIELD, 0.8),
])
@pytest.mark.parametrize("period_id", ["202401", "202402", "202407", "202409"])
def test_calc_accurary_by_half_year(period_id, metric, metric_true, expected_acc):
    chanye_code = "chanye_code"
    chanye_level = "LV1"
    bg_name = "bg_name"
    bg_code = "bg_code"
    oversea_desc = "全球"
    currency = "CNY"
    data = {
        "period_id": period_id,
        "chanye_code": chanye_code,
        "chanye_level": chanye_level,
        "bg_name": bg_name,
        "bg_code": bg_code,
        "oversea_desc": oversea_desc,
        "currency": currency,
    }
    num = 6
    hist_df = pd.DataFrame({
        "period_id": ["202301", "202302", "202304", "202307", "202309", "202312"],
        "target_period": ["2023H1"] * 3 + ["2023H2"] * 3,
        "chanye_code": [chanye_code] * num,
        "chanye_level": [chanye_level] * num,
        "bg_name": [bg_name] * num,
        "bg_code": [bg_code] * num,
        "currency": [currency] * num,
        "oversea_desc": [oversea_desc] * num,
        AI_MGP_RATE_FIELD: [0.7, 0.9, 0.7, 0.9, 0.7, 0.9],
        AI_EQUIP_REV_AMT_FIELD: [120, 120, 80, 120, 80, 120],
        MGP_RATE_FIELD: [0.7, 0.9, 0.7, 0.9, 0.7, 0.9],
        EQUIP_REV_AMT_FIELD: [120, 120, 80, 120, 80, 120],
        FACT_MGP_RATE_FIELD: [0.8] * num,
        FACT_EQUIP_REV_AMT_FIELD: [100] * num,
    })
    acc = calc_accurary_by_half_year(data, metric, metric_true, hist_df)
    case.assertEqual(round(acc, 2), round(expected_acc, 2))


@pytest.mark.parametrize("metric, metric_true, expected_acc", [
    (AI_MGP_RATE_FIELD, FACT_MGP_RATE_FIELD, 0.9),
    (AI_EQUIP_REV_AMT_FIELD, FACT_EQUIP_REV_AMT_FIELD, 0.8),
    (MGP_RATE_FIELD, FACT_MGP_RATE_FIELD, 0.9),
    (EQUIP_REV_AMT_FIELD, FACT_EQUIP_REV_AMT_FIELD, 0.8),
])
@pytest.mark.parametrize("period_id", ["202401", "202402", "202403"])
def test_calc_accurary_by_quarter(period_id, metric, metric_true, expected_acc):
    chanye_level = "LV1"
    chanye_code = "chanye_code"
    oversea_desc = "全球"
    currency = "CNY"
    bg_name = "bg_name"
    bg_code = "bg_code"
    data = {
        "period_id": period_id,
        "chanye_level": chanye_level,
        "chanye_code": chanye_code,
        "oversea_desc": oversea_desc,
        "currency": currency,
        "bg_name": bg_name,
        "bg_code": bg_code,
    }
    num = 6
    hist_df = pd.DataFrame({
        "period_id": ["202301", "202302", "202303", "202310", "202311", "202312"],
        "target_period": ["2023Q1"] * 3 + ["2023Q4"] * 3,
        "chanye_code": [chanye_code] * num,
        "chanye_level": [chanye_level] * num,
        "bg_name": [bg_name] * num,
        "bg_code": [bg_code] * num,
        "oversea_desc": [oversea_desc] * num,
        "currency": [currency] * num,
        AI_MGP_RATE_FIELD: [0.7, 0.9, 0.7, 0.9, 0.7, 0.9],
        AI_EQUIP_REV_AMT_FIELD: [120, 120, 80, 120, 80, 120],
        MGP_RATE_FIELD: [0.7, 0.9, 0.7, 0.9, 0.7, 0.9],
        EQUIP_REV_AMT_FIELD: [120, 120, 80, 120, 80, 120],
        FACT_MGP_RATE_FIELD: [0.8] * num,
        FACT_EQUIP_REV_AMT_FIELD: [100] * num,
    })
    acc = calc_accurary_by_quarter(data, metric, metric_true, hist_df)
    case.assertEqual(round(acc, 2), round(expected_acc, 2))


@pytest.mark.parametrize("period_id,not_null", [
    ("202401", True),
    ("202402", True),
    ("202403", True),
    ("202404", False),
])
def test_query_hist_data_in_dataframe(period_id, not_null):
    target_period = "2024"
    bg_name = "bg_name"
    bg_code = "bg_code"
    chanye_level = "LV1"
    chanye_code = "chanye_code"
    oversea_desc = "全球"
    currency = "CNY"
    data = {
        "bg_code": bg_code,
        "bg_name": bg_name,
        "chanye_level": chanye_level,
        "chanye_code": chanye_code,
        "oversea_desc": oversea_desc,
        "currency": currency,
    }
    hist_df = pd.DataFrame({
        "period_id": ["202401", "202402", "202403", "202403"],
        "target_period": [target_period] * 4,
        "bg_code": [bg_code] * 4,
        "bg_name": [bg_name] * 4,
        "chanye_level": [chanye_level] * 4,
        "chanye_code": [chanye_code] * 4,
        "oversea_desc": [oversea_desc] * 4,
        "currency": [currency] * 4,
    })
    res = query_hist_data_in_dataframe(data, period_id, target_period, hist_df)
    print(period_id)
    print(res)
    case.assertEqual(len(res) > 0, not_null)


@pytest.mark.parametrize("metric, metric_true, expected_acc", [
    (AI_MGP_RATE_FIELD, FACT_MGP_RATE_FIELD, 0.9),
    (AI_EQUIP_REV_AMT_FIELD, FACT_EQUIP_REV_AMT_FIELD, 0.98),
])
def test_calc_accurary(metric, metric_true, expected_acc):
    metric1 = AI_MGP_RATE_FIELD
    metric1_fact = FACT_MGP_RATE_FIELD
    metric2 = AI_EQUIP_REV_AMT_FIELD
    metric2_fact = FACT_EQUIP_REV_AMT_FIELD
    res_dict = {
        metric1: 0.9,
        metric1_fact: 0.8,
        metric2: 102,
        metric2_fact: 100
    }
    acc = calc_accurary(metric, metric_true, res_dict)
    case.assertEqual(expected_acc, acc)


@pytest.mark.parametrize("data_type, metric, expected_w", [
    ("ai", "m1", 0.33),
    ("ai", "m2", 0.5),
    ("ai", "m3", 0.67),
    ("expert", "m1", 0.67),
    ("expert", "m2", 0.5),
    ("expert", "m3", 0.33),
    ("ai", "m4", None),
])
def test_calc_weight_by_softmax(data_type, metric, expected_w):
    df = pd.DataFrame({
        "ai_acc_m1": [0.8],
        "expert_acc_m1": [0.9],
        "ai_acc_m2": [0.8],
        "expert_acc_m2": [0.8],
        "ai_acc_m3": [0.9],
        "expert_acc_m3": [0.8],

    })
    s = df.iloc[0, :]
    w = calc_weight_by_softmax(s, metric, data_type)
    print(w)


@pytest.mark.parametrize("metric, expected_weight", [
    ("m1", 1.0),
    ("m2", None),
    ("m3", 0.5),
])
def test_calc_weight(metric, expected_weight):
    df = pd.DataFrame({
        "ai_acc_m1": [None],
        "ai_weight_m1": [None],
        "expert_acc_m1": [0.9],
        "ai_acc_m2": [None],
        "ai_weight_m2": [None],
        "expert_acc_m2": [None],
        "ai_acc_m3": [0.8],
        "ai_weight_m3": [0.5],
        "expert_acc_m3": [0.9],

    })
    s = df.iloc[0, :]
    w = calc_weight(s, metric)
    case.assertEqual(expected_weight, w)


@pytest.mark.parametrize("metric, expected_fcst", [
    ("m1", 0.88),
    ("m2", 0.7),
    ("m3", 0.8),
    ("m4", None),
])
def test_combined_fcst_by_weight(metric, expected_fcst):
    df = pd.DataFrame({
        "ai_weight_m1": [0.8],
        "expert_weight_m1": [0.2],
        "m1": [0.9],
        "expert_m1": [0.8],
        "ai_weight_m2": [0.5],
        "expert_weight_m2": [0.5],
        "m2": [0.8],
        "expert_m2": [0.7],
        "ai_weight_m3": [None],
        "expert_weight_m3": [1.],
        "m3": [None],
        "expert_m3": [0.8],
        "ai_weight_m4": [None],
        "expert_weight_m4": [None],
        "m4": [None],
        "expert_m4": [None],

    })
    s = df.iloc[0, :]
    ai_metric = metric
    expert_metric = "expert_" + metric
    combined_fcst_by_weight(s, ai_metric, expert_metric)


# @pytest.mark.skipif(os.name != 'nt', reason="Linux环境暂不执行")
def test_combine_expert_result(mocker):
    # 测试数据加载
    ai_fcst_df = pd.read_csv(pathlib.Path(__file__).parent.joinpath(
        "data/test_data_combined_ai_fcst.csv"
    ))
    group_expert_fcst_df = pd.read_csv(pathlib.Path(__file__).parent.joinpath(
        "data/test_data_combined_group_expert.csv"
    ))
    chanye_expert_fcst_df = pd.read_csv(pathlib.Path(__file__).parent.joinpath(
        "data/test_data_combined_chanye_expert.csv"
    ))
    fact_df = pd.read_csv(pathlib.Path(__file__).parent.joinpath(
        "data/test_data_combined_fact.csv"
    ))

    # 空值填充
    ai_fcst_df.fillna(NULL_STRING_PLACE_HOLDER, inplace=True)
    group_expert_fcst_df.fillna(NULL_STRING_PLACE_HOLDER, inplace=True)
    chanye_expert_fcst_df.fillna(NULL_STRING_PLACE_HOLDER, inplace=True)
    fact_df.fillna(NULL_STRING_PLACE_HOLDER, inplace=True)

    # mock从数据库读取数据
    mock_func_path = "profitforecast.utils.combine_data.read_to_combine_data_from_db"
    mocker.patch(mock_func_path, return_value=(ai_fcst_df, [group_expert_fcst_df, chanye_expert_fcst_df], fact_df))

    # 调用函数
    period_id = "202403"
    run_env = "uat"
    db_table_names = (
        AI_FCST_TBL_NAME,
        [GROUP_EXPERT_FCST_TBL_NAME, CHANYE_EXPERT_FCST_TBL_NAME],
        FACT_TBL_NAME
    )
    expert_data_type = "both"
    hist_merged_df, combined_df = combine_expert_result(
        db_table_names,
        period_id,
        run_env,
        expert_data_type
    )
    print(combined_df)
    print(hist_merged_df)


@pytest.mark.skipif(os.name != 'nt', reason="Linux环境暂不执行")
@pytest.mark.parametrize("expert_type", [
    GROUP_EXPERT_TYPE,
    CHANYE_EXPERT_TYPE,
])
def test_combine_one_expert_result(mocker, expert_type):
    # 测试数据加载
    ai_fcst_df = pd.read_csv(pathlib.Path(__file__).parent.joinpath(
        "data/test_data_combined_ai_fcst.csv"
    ))
    if expert_type == GROUP_EXPERT_TYPE:
        expert_fcst_df = pd.read_csv(pathlib.Path(__file__).parent.joinpath(
            "data/test_data_combined_group_expert.csv"
        ))
    else:
        expert_fcst_df = pd.read_csv(pathlib.Path(__file__).parent.joinpath(
            "data/test_data_combined_chanye_expert.csv"
        ))
    fact_df = pd.read_csv(pathlib.Path(__file__).parent.joinpath(
        "data/test_data_combined_fact.csv"
    ))

    # 空值填充
    ai_fcst_df.fillna(NULL_STRING_PLACE_HOLDER, inplace=True)
    expert_fcst_df.fillna(NULL_STRING_PLACE_HOLDER, inplace=True)
    fact_df.fillna(NULL_STRING_PLACE_HOLDER, inplace=True)

    # mock从数据库读取数据
    mock_func_path = "profitforecast.utils.combine_data.read_to_combine_data_from_db"
    mocker.patch(mock_func_path, return_value=(ai_fcst_df, [expert_fcst_df], fact_df))

    # 调用函数
    period_id = "202403"
    run_env = "uat"
    expert_table_names = [GROUP_EXPERT_FCST_TBL_NAME] if expert_type == GROUP_EXPERT_TYPE \
        else CHANYE_EXPERT_FCST_TBL_NAME
    db_table_names = (
        AI_FCST_TBL_NAME,
        expert_table_names,
        FACT_TBL_NAME
    )
    expert_data_type = expert_type
    hist_merged_df, combined_df = combine_expert_result(
        db_table_names,
        period_id,
        run_env,
        expert_data_type
    )
    print(combined_df)
    print(hist_merged_df)


def test_preprocess_expert_fcst_data():
    chanye_expert_data_file = "data/test_data_prod_fcst.csv"
    chanye_expert_df = pd.read_csv(pathlib.Path(__file__).parent.joinpath(
        chanye_expert_data_file
    ))
    chanye_expert_df.fillna(NULL_STRING_PLACE_HOLDER, inplace=True)
    chanye_expert_df["period_id"] = chanye_expert_df["period_id"].astype(str)

    fact_data_file = "data/test_data_act.csv"
    fact_df = pd.read_csv(pathlib.Path(__file__).parent.joinpath(
        fact_data_file
    ))
    fact_df.fillna(NULL_STRING_PLACE_HOLDER, inplace=True)
    fact_df["period_id"] = fact_df["period_id"].astype(str)

    expert_fcst_df_list = [
        pd.DataFrame(columns=chanye_expert_df.columns),
        chanye_expert_df,
    ]
    expert_fcst_tbl_names = [GROUP_EXPERT_FCST_TBL_NAME, CHANYE_EXPERT_FCST_TBL_NAME]
    time = "202406"

    preprocess_expert_fcst_data(expert_fcst_df_list, expert_fcst_tbl_names, fact_df, time)
    print(expert_fcst_df_list[1])
    df = expert_fcst_df_list[1]
    case.assertEqual(
        df[(df["bg_name"] == "政企") & (df["lv1_name"] == "数据通信") & (df["period_id"] == "202406")]["mgp_ratio"].mean(),
        0.6666
    )
    case.assertEqual(
        df[(df["bg_name"] == "ICT") & (df["lv1_name"] == "无线") & (df["period_id"] == "202406")]["mgp_ratio"].mean(),
        0.6666
    )
