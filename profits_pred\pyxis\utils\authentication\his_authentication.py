import base64
import json

import requests
from cachetools.func import ttl_cache
from tenacity import retry, retry_if_not_exception_type, stop_after_attempt, wait_fixed


class HisTokenError(Exception):
    def __init__(self):
        super().__init__()


@ttl_cache(maxsize=128, ttl=300)
@retry(
    retry=retry_if_not_exception_type(HisTokenError), stop=stop_after_attempt(3),
    wait=wait_fixed(1), reraise=True
)
def soa_verify_token(url: str, basic_token: str) -> None:
    response = requests.post(
        url,
        json={"basicToken": basic_token},
        headers={"Content-Type": "application/json"},
        timeout=1
    )
    if response.status_code != 200:
        raise HisTokenError()
    result = json.loads(response.text)
    auth_result = result["result"]
    if auth_result is not True:
        raise HisTokenError()


class HisSubscriptionError(Exception):
    def __init__(self):
        super().__init__()


@ttl_cache(maxsize=128, ttl=300)
@retry(
    retry=retry_if_not_exception_type(HisSubscriptionError), stop=stop_after_attempt(3),
    wait=wait_fixed(1), reraise=True
)
def soa_verify_uri(url: str, basic_token: str, uri: str) -> None:
    try:
        app_id = str(base64.b64decode(basic_token[len("Basic "):len(basic_token)]), "utf8").split(":")[0]
    except Exception:
        app_id = basic_token

    response = requests.post(
        url,
        json={"uri": uri, "appAlias": app_id},
        headers={"Content-Type": "application/json"},
        timeout=1
    )
    if response.status_code != 200:
        raise HisSubscriptionError()
    result = json.loads(response.text)
    auth_result = result["result"]
    if auth_result is not True:
        raise HisSubscriptionError()
