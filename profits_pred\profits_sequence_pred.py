import base64
import io
import json
import os
import time
from io import BytesIO
from multiprocessing import Pool
from typing import List, Union, Tuple, Any, Dict

import numpy as np
import pandas as pd
import requests
from AutoTsPred.experiment.profit_pipeline import rule_profit_pipeline
from aipaas.env import env_mode
from his_decrypt import EncryptType

from conditional_line_trend_judge import conditional_line_trend
from conditional_post_process import IncreaseInCycle
from dimension_constant import unit_cost_solutionId, unit_price_solutionId, other_param_solutionId, problemId, \
    taskId
from profit_exception_pipeline import check_pipeline, reference_pipline_trend
from profits_pred.pyxis.utils.authorization.his_authorization import HisAuthorizationError
from profits_pred.pyxis.utils.config_center.his_config_center_client import decrypt
from profits_pred.pyxis.utils.config_template import Config
from profits_pred.src.utils.resource_loader import LOGGER as log

dim_need_interval_prediction = ['unit_cost', 'unit_price']
nondim_need_interval_prediction = ['mgp_ratio']
lv2nodim_need_interval_prediction = ['mgp_ratio_after', 'equip_rev_cons_after_amt']
indicators_need_interval_prediction = dim_need_interval_prediction + nondim_need_interval_prediction + lv2nodim_need_interval_prediction


def months_until_next_year_end(pred_version):
    """
    计算从当前期到同年12月的月份个数
    """
    pred_version = str(pred_version)
    pred_month = int(pred_version[-2:])
    # 计算当前月份到12月的月份个数
    if pred_month < 1 or pred_month > 12:
        raise ValueError("月份必须在 1 到 12 之间")
    #
    if pred_month in [11, 12]:
        months_count = 12 - pred_month + 13
    else:
        months_count = 12 - pred_month + 1

    return months_count


def params_for_rule_profit_pipeline(target_name: str, pred_version: int):
    # 不同指标的评估函数、变点检测参数不同
    if target_name in ["unit_cost", "unit_price"]:
        metric, bayes_p_value = "mape", 0.05
    elif target_name in ["equip_rev_cons_after_amt"]:
        metric, bayes_p_value = "mape", 0.1
    else:
        metric, bayes_p_value = "mae", 0.01
    # 是否需要做变点检测，当吧bayes_p_value>1，即无论如何都不会检测出变点，这里用于表示是否需要变点检测，下面这些指标不需要做变点检测
    if target_name in ["mca_adjust_ratio", "mgp_adjust_ratio", "unit_price", "unit_cost",
                       "carryover_rate", "rev_percent"]:
        bayes_p_value = 1.1
    # 不同指标、使用的模型不同
    if target_name in ["unit_price"]:
        model_nums = ["auto_ets", "prophet", "naive"]
    else:
        model_nums = ["arima", "auto_ets", "prophet", "naive"]
    # 不同指标模型参数不同，autoarima的季节性参数都是False
    seasonal = False
    # 不同指标模型参数不同，这里的参数用于调整，YTD法预测结果
    if target_name in ["equip_rev_cons_after_amt"]:
        adjust_tag = True
    else:
        adjust_tag = False
    # 不同指标模型参数不同, 这里调节是否需要异常检测，线性外推检测参数
    if target_name in ["equip_rev_cons_after_amt", "mca_adjust_ratio", "mgp_adjust_ratio", "carryover_rate"]:
        anomaly_entry = "not_bayes_cycle_test"  # 不做异常检测
        trend_thold = 0.65  # 线性外推参数
    else:
        anomaly_entry = "bayes_cycle_test"  # 做异常检测
        trend_thold = 0.8  # 线性外推参数
    ignore_month = [11, 12]  # ytd法
    steps = months_until_next_year_end(pred_version)
    param_set = (anomaly_entry, ignore_month, trend_thold, adjust_tag,
                 bayes_p_value, model_nums, metric, seasonal, steps)
    params = get_params_for_rule(param_set)
    return params

def get_params_for_rule(param_set):
    (anomaly_entry, ignore_month, trend_thold, adjust_tag, bayes_p_value,
     model_nums, metric, seasonal, steps) = param_set
    params = {
        # 异常检测
        "anomaly_entry_conditions": anomaly_entry, "anomaly_model_name": "three_sigma",
        "anomaly_kwargs": {
            "thold": 4, "post_process_method": "ignore_anomaly_by_month",  # 指定异常检测后处理方法
            "ignore_month_list": ignore_month},  # 指定要忽略的异常月份
        "reference_detection": True, "line_trend_thres": trend_thold, "adjust_tag": adjust_tag,
        # 变点检测
        "bayes_p_value": bayes_p_value, "is_truncated": True, "ignore_last_n": 13,  # 截断数据时忽略最后n个时点中识别出的变点
        # 短时序
        "short_ts_threshold": 5,  # 短时序判断阈值，大于阈值就不是短时序
        # 数据集划分 - 暂时写死
        "fixed_test_size": 6, "models": model_nums, "eval_metric": metric,  # 点预测评估指标
        "confidence": 0.95, "ensemble_method": "weight", "plot": None,
        "model_params": {
            "arima": {"seasonal": seasonal},
            "naive": {"sp": 12}, "auto_ets": {"seasonal_periods": 12, "params": {"yearly_accumulation": True}},
            "prophet": {},
        },
        "steps": steps, "predict_rule": "fill_data"
    }
    return params


def params_for_crd(target_name):
    """
    条件可参考性判断的参数
    """
    # 不同指标的评估函数、条件可参考性参数不同
    if target_name in ["unit_cost", "unit_price"]:
        bayes_p_value = 0.05
    elif target_name in ["equip_rev_cons_after_amt"]:
        bayes_p_value = 0.1
    else:
        bayes_p_value = 0.01
    # 是否需要做条件可参考性检测，当吧bayes_p_value>1，即无论如何都不会检测出变点，这里用于表示是否需要变点检测，下面这些指标不需要做变点检测
    if target_name in ["mca_adjust_ratio",
                       "mgp_adjust_ratio",
                       "unit_price",
                       "unit_cost",
                       "carryover_rate",
                       "rev_percent"]:
        bayes_p_value = 1.1
    params = {
        # 变点检测
        "bayes_p_value": bayes_p_value,  # 贝叶斯变点检测p值
        "is_truncated": True,  # 是否按变点截断数据
        "ignore_last_n": 13,  # 截断数据时忽略最后n个时点中识别出的变点
    }
    return params

def params_for_cad(target_name):
    """
    包括：异常检测、数据可参考性检测、变点检测
    :param target_name: 预测参数名
    :return:
    """
    # 不同指标的评估函数、变点检测参数不同
    if target_name in ["unit_cost", "unit_price"]:
        bayes_p_value = 0.05
    elif target_name in ["equip_rev_cons_after_amt"]:
        bayes_p_value = 0.1
    else:
        bayes_p_value = 0.01

    # 是否需要做变点检测，当吧bayes_p_value>1，即无论如何都不会检测出变点，这里用于表示是否需要变点检测，下面这些指标不需要做变点检测
    if target_name in ["mca_adjust_ratio",
                       "mgp_adjust_ratio",
                       "unit_price",
                       "unit_cost",
                       "carryover_rate",
                       "rev_percent"]:
        bayes_p_value = 1.1

    # 不同指标模型参数不同，这里的参数用于调整，YTD法预测结果
    if target_name in ["equip_rev_cons_after_amt"]:
        adjust_tag = True
    else:
        adjust_tag = False

    # 不同指标模型参数不同, 这里调节是否需要异常检测
    if target_name in ["equip_rev_cons_after_amt",
                       "mca_adjust_ratio",
                       "mgp_adjust_ratio",
                       "carryover_rate"
                       ]:
        anomaly_entry = "not_bayes_cycle_test"  # 不做异常检测
    else:
        anomaly_entry = "bayes_cycle_test"  # 做异常检测
    ignore_month = [11, 12]  # ytd法

    params = {
        # 异常检测
        "anomaly_entry_conditions": anomaly_entry,  # True,
        "anomaly_model_name": "three_sigma",
        "anomaly_kwargs": {
            "thold": 4,
            "post_process_method": "ignore_anomaly_by_month",  # 指定异常检测后处理方法
            "ignore_month_list": ignore_month,  # 指定要忽略的异常月份
        },
        "reference_detection": True,
        # 变点检测
        "bayes_p_value": bayes_p_value,  # 贝叶斯变点检测p值
        "is_truncated": True,  # 是否按变点截断数据
        "ignore_last_n": 13,  # 截断数据时忽略最后n个时点中识别出的变点
    }
    return params


def process_row(
        df: pd.DataFrame,
        period_col: str,
        data_col: str,
        image_name: Union[None, str],
        pred_version: int
) -> Tuple[pd.DataFrame, pd.DataFrame, np.ndarray, dict, str]:
    """
    处理单个维度+预测col=预测对象的时序
    处理成Time,Data,UNION_ATTR,没有Miss,有ytd_data
    预测返回5种数据类型
    """
    if df.empty:
        empty_df = pd.DataFrame(columns=['Time', 'y_hat'])
        return empty_df, empty_df, np.array([]), {}, "Missing dimensions"
    # 处理成Time,Data,UNION_ATTR, 没有：Miss,如果线性外推趋势，如果是那几个指标需要有：ytd_data
    df['Time'] = df[period_col].apply(
        lambda x: f"{str(x)[:4]}-{str(x)[4:6]}" if str(x).endswith('YTD') else str(x)[:-3])
    df['UNION_ATTR'] = image_name
    df['Data'] = df[data_col]
    # 向前填充空值,其余空值置0
    df.sort_values(by='Time', inplace=True)
    df['Data'] = df['Data'].fillna(method='ffill')
    df['Data'] = pd.to_numeric(df['Data'], errors='coerce')
    df['Data'] = df['Data'].fillna(0)
    params = params_for_rule_profit_pipeline(data_col, pred_version)
    if data_col + '_ytd_data' in list(df.columns):
        df['ytd_data'] = df[data_col + '_ytd_data']
        if not (df['ytd_data'].empty or df['ytd_data'].isna().all()):
            _df = df[['Time', 'Data', 'ytd_data', 'UNION_ATTR']].reset_index(drop=True)
        else:
            _df = df[['Time', 'Data', 'UNION_ATTR']].reset_index(drop=True)
    else:
        _df = df[['Time', 'Data', 'UNION_ATTR']].reset_index(drop=True)
    a, b, c, d, e = rule_profit_pipeline(_df, params)
    return a, b, c, d, e




def get_dynamic_token() -> str:
    # url="http://kwe-beta.huawei.com/ApiCommonQuery/appToken/getRestAppDynamicToken"#---读取一下json todo
    # app_id="com.huawei.finance.ai.opt.fop"
    if str(env_mode).lower() == 'prod':
        api_file = "src/config/PROD-AIF.json"
    else:
        api_file = "src/config/DEV-AIF.json"
    with open(os.path.join(os.path.dirname(__file__),api_file), "r", encoding="utf-8") as f:#--获取环境变量  todo
        data = json.load(f)
    config = Config(**data)
    url = config.deploy.api_gateway.authorization.url
    app_id = config.deploy.config_server.app_id

    static_token=decrypt(
                    config_parts=config.deploy.config_server.config_parts,
                    work_key_cipher=os.environ.get("WORK_KEY_CIPHER", str()),
                    text=os.environ.get("STATIC_TOKEN", str()),
                    encrypt_type=EncryptType.ADV_2_6
                )
    info = {
        "appId": app_id,
        "credential": str(base64.b64encode(bytes(static_token, "utf-8")), "utf-8")
    }

    response = requests.post(
        url,
        json=info, headers={"Content-Type": "application/json"},
        timeout=1
    )
    if response.status_code != 200:
        raise HisAuthorizationError()
    result = json.loads(response.text)
    dynamic_token = result["result"]
    if dynamic_token is None:
        raise HisAuthorizationError()
    return dynamic_token


def tspred(df: pd.DataFrame,
           target_name: str, pred_version: int, image_name: Union[None, str]) -> pd.DataFrame:

    def get_api_params(data_col: str) -> int:
        # 均本-损益收入:  problemId：585942，taskId：585942，solutionId：11306
        # 均价:         problemId：585942，taskId：585942，solutionId：11308
        # 结转率:        problemId：585942，taskId：585942，solutionId：11310
        unit_cost_rev_col = ['unit_cost', 'equip_rev_cons_after_amt']
        unit_price_col = ['unit_price']
        other_col = ["carryover_rate", "rev_percent", "mca_adjust_ratio", "mgp_adjust_ratio",
                     'mgp_ratio_after', 'mgp_ratio']
        # problemId = 585942
        # taskId = 585942
        solutionId = None
        if data_col in unit_cost_rev_col:
            solutionId = unit_cost_solutionId
        elif data_col in unit_price_col:
            solutionId = unit_price_solutionId
        elif data_col in other_col:  # 对应其他预测参数
            solutionId = other_param_solutionId

        if solutionId is None:
            raise Exception(f'solutionId参数获取错误，无{target_name}对应的solutionId参数')
        return solutionId


    def read_config() -> json:
        """读取配置文件"""
        if str(env_mode).lower() == 'prod':
            # api_file = "src/config/PROD-API.json"
            api_file = os.path.join(os.path.dirname(os.path.abspath(__file__)),'src','config','PROD-API.json')
        else:
            # api_file = "src/config/DEV-API.json"
            api_file = os.path.join(os.path.dirname(os.path.abspath(__file__)),'src','config','DEV-API.json')

        try:
            with open(api_file, "r", encoding="utf-8") as f:
                api_config = json.load(f)
        except FileNotFoundError:
            raise ValueError(f"环境 '{env_mode}' 的配置文件不存在: {api_file}")
        except json.JSONDecodeError:
            raise ValueError(f"配置文件格式错误: {api_file}")
        return api_config


    def Upload_Predict_getVersionId(url: str,
                                    trainSet: bytes,
                                    testSet: bytes,
                                    api_config: json
                                    ) -> int:
        request_headers = {
            'Authorization': get_dynamic_token(),
            "Referer": referer
        }
        body = {
            "problemId": problemId,
            "solutionId": solutionId,
            "testTargetFlag": "N",
            "modelFlag": "N",
            "taskId": taskId
        }
        files = {
            "trainSet": ("train.csv", trainSet),
            "testSet": ("test.csv", testSet),
        }
        timeout = api_config.get("request_max_timeout") #---放在环境变量 todo

        versionId = None
        response = requests.post(url, headers=request_headers, data=body, files=files, timeout=timeout, verify=False)
        if response.status_code == 200: #-----错误措施 todo
            result = response.json()
            versionId = result.get('data')
        else:
            log.error("上传预测文件失败：{}，problem_id:{}".format(response.status_code,problemId))
            raise requests.exceptions.HTTPError()
        return versionId


    def get_indicator_forecast(url, referer, problemId, solutionId, version_id, api_config, dynamic_token):
        """
        获取预测结果api调用
        :param problemId:
        :param solutionId:
        :param version_id:
        :return:
        """
        request_headers = {
            'Authorization': dynamic_token,
            "Referer": referer
        }
        params = {
            "problemId": problemId,
            "solutionId": solutionId,
            "versionId": version_id,
        }
        max_timeout = api_config.get("request_max_timeout")
        data = None
        response = None
        try:
            response = requests.get(url, params=params, headers=request_headers, timeout=max_timeout, verify=False)
            if response.status_code == 200:#---错误措施 todo: 如果其他状态说明请求失败返回None
                result = response.json()
                if result:
                    http_code = result.get("httpCode")
                    if http_code == 200:
                        data = result.get("data")
                        log.info("获取预测文件url成功----------")
                else:
                    log.info("预测结果为空")
            elif response.status_code == 500:  # 如果500直接抛出异常不再轮询
                raise requests.exceptions.HTTPError()
            else:
                log.error("获取预测文件url错误-response:{}".format(response.json()))
                log.error(
                    "异常请求参数：problemId:{},solutionId:{},versionId:{}".format(problemId, solutionId, versionId))
        except Exception as e:
            log.error(
                "异常请求参数：problemId:{},solutionId:{},versionId:{}".format(problemId, solutionId, versionId))
            if response.status_code == 500:
                raise e
        return data


    def get_ResultUrl(url, versionId, api_config):
        # max_duration = 300  # 改成环境变量传入-----------------------TODO
        max_duration = api_config.get("max_duration")
        # interval = 30  # 改成环境变量传入---------------------------TODO
        interval = api_config.get("interval")
        start_time = time.time()
        end_time = start_time + max_duration
        dynamic_token = get_dynamic_token()
        res_url = None
        remaining = None
        while time.time() < end_time:
            try:
                # 计算剩余时间并动态调整休眠时间
                remaining = end_time - time.time()
                sleep_time = min(interval, remaining)
                time.sleep(sleep_time)
                res_url = get_indicator_forecast(url, referer, problemId, solutionId, versionId, api_config, dynamic_token)
                if res_url:
                    return res_url
            except Exception as e:
                log.error(f"api获取预测结果发生异常: {str(e)}")
                log.error("api异常请求参数：problemId:{},solutionId:{},versionId:{},报错原因：{}".format(problemId, solutionId, versionId, e))
                return None
        consum_time = max_duration - remaining
        if not res_url:
            log.error('api获取预测结果请求超时-url:{}, versionId:{}-problemId:{}, 耗时：{}'.format(res_url, versionId, problemId,
                                                                                                   consum_time))
        else:
            log.info('api获取预测结果正常-url:{}, versionId:{}-problemId:{}, 耗时：{}'.format(res_url, versionId, problemId,
                                                                                         consum_time))
        return res_url


    def df_to_csv_buffer(df_train: pd.DataFrame, pred_col, image_name: Union[None, str]) -> Tuple[bytes, bytes]:
        """
        数据处理函数
        参数:
            df: 原始数据DataFrame
            target_name: 目标列名
        返回:
            tuple: (train_data_csv_bytes, test_data_csv_bytes)
        """
        df_test = pd.DataFrame(columns=df_train.columns)
        df_train["Time"] = pd.to_datetime(df_train["Time"], format="%Y-%m")
        max_date = df_train['Time'].max() + pd.DateOffset(months=1)
        new_dates = pd.date_range(start=max_date, periods=steps, freq='MS')
        df_test['Time'] = new_dates
        df_train.sort_values(by="Time", inplace=True)
        df_test.sort_values(by="Time", inplace=True)
        df_train.drop_duplicates(['Time'], inplace=True)
        df_test.drop_duplicates(['Time'], inplace=True)
        df_train.rename(columns={target_name: "Data"}, inplace=True)
        df_test.rename(columns={target_name: "Data"}, inplace=True)
        df_train.reset_index(drop=True, inplace=True)
        df_test.reset_index(drop=True, inplace=True)
        mon_continue = is_monthly_continuous(df_train,df_test)  # 考虑到耗时时可以去掉该判断
        if not mon_continue:
            log.error("训练集测试集是否是否连续：{},预测参数：{}".format(mon_continue, pred_col))
            log.error("image_name:{}".format(image_name))
            log.error("train_Time:{},Test_time{}".format(df_train['Time'].tolist(), df_test['Time'].tolist()))
        if len(df) != len(df_train):
            log.error("训练集存在重复数据：{},预测参数：{}".format(mon_continue, pred_col))
            log.error("image_name:{}".format(image_name))
            log.error("train_Time:{},Test_time{}".format(df_train['Time'].tolist(), df_test['Time'].tolist()))
        csv_buffer_train = BytesIO()
        df_train.to_csv(csv_buffer_train, index=False)
        csv_buffer_train.seek(0)  # 将指针重置到文件开头

        csv_buffer_test = BytesIO()
        df_test.to_csv(csv_buffer_test, index=False)
        csv_buffer_test.seek(0)  # 将指针重置到文件开头
        return csv_buffer_train, csv_buffer_test

    api_config =read_config()
    upload_url = api_config.get("upload_url")
    referer = api_config.get("Referer")
    solutionId = get_api_params(target_name)
    steps = months_until_next_year_end(pred_version)
    csv_buffer_train, csv_buffer_test = df_to_csv_buffer(df, pred_col=target_name, image_name=image_name)
    versionId = Upload_Predict_getVersionId(url=upload_url, trainSet=csv_buffer_train, testSet=csv_buffer_test, api_config=api_config)
    forecast_result_url = api_config.get("forecast_result_url")

    result_url = get_ResultUrl(forecast_result_url, versionId, api_config)
    # 根据url获取预测文件
    file_data = requests.get(result_url, timeout=api_config.get("request_max_timeout"), verify=False)#---TODO
    predict_df = pd.read_csv(io.StringIO(file_data.text))
    if predict_df.empty:
        log.error("api预测返回文件为空：versionId:{}-solutionId{}-image_name{}, api预测文件shape：{}".format(
            versionId, solutionId, image_name, predict_df.shape))
    return predict_df


def process_data(df: pd.DataFrame, period_col: str, data_col: str,
                    image_name: Union[None, str]):
    '''
    处理数据
    :param df:
    :param period_col:
    :param data_col:
    :param image_name:
    :return:
    '''

    # 处理成Time,Data,UNION_ATTR,
    # 没有：Miss,
    # 如果线性外推趋势，如果是那几个指标需要有：ytd_data
    df['Time'] = df[period_col].apply(
        lambda x: f"{str(x)[:4]}-{str(x)[4:6]}" if str(x).endswith('YTD') else str(x)[:-3])
    df['UNION_ATTR'] = image_name
    df['Data'] = df[data_col]
    # 向前填充空值,其余空值置0
    df.sort_values(by='Time', inplace=True)
    df['Data'] = df['Data'].fillna(method='ffill')
    df['Data'] = pd.to_numeric(df['Data'], errors='coerce')
    df['Data'] = df['Data'].fillna(0)
    # 获取填充值
    fill_value = df['Data'].iloc[-1]

    _df = ytd_add(df, data_col)
    return _df, fill_value


def ytd_add(df, data_col):
    if data_col + '_ytd_data' in list(df.columns):
        df['ytd_data'] = df[data_col + '_ytd_data']
        if not (df['ytd_data'].empty or df['ytd_data'].isna().all()):
            _df = df[['Time', 'Data', 'ytd_data', 'UNION_ATTR']].reset_index(drop=True)
        else:
            _df = df[['Time', 'Data', 'UNION_ATTR']].reset_index(drop=True)
    else:
        _df = df[['Time', 'Data', 'UNION_ATTR']].reset_index(drop=True)
    return _df


def process_row_api(df: pd.DataFrame, period_col: str, data_col: str,
                    image_name: Union[None, str], pred_version: int) -> Tuple[pd.DataFrame, pd.DataFrame, np.ndarray, dict, str]:
    """
    :param df:
    :param period_col:
    :param data_col:
    :param image_name:
    :return: pred, pi_pred, pi_dist, mid_data, msg
    pred：点预测-两列
    pi_pred:区间预测-三列
    mid_data：字典{pi_confidence：’0.95‘，'eval_result'： 0 }
    """
    pred = None
    try:
        if df.empty:
            empty_pred = pd.DataFrame(columns=['Time', 'y_hat'])
            empty_pi_pred = pd.DataFrame(columns=['Time', 'yhat_upper', 'yhat_lower'])
            return empty_pred, empty_pi_pred, np.array([]), {}, "Missing dimensions"
        _df, fill_value = process_data(df, period_col, data_col, image_name)
        params = params_for_rule_profit_pipeline(data_col, pred_version)
        if len(_df) < 11:  # 短时序
            _df = ytd_add(df, data_col)
            pred, pi_pred, pi_dist, mid_data, msg = rule_profit_pipeline(_df, params)
            log.info("短时序预测成功")
        else:
            reference_param = params_for_crd(data_col)
            reference_tag = reference_pipline_trend(_df, reference_param)
            line_tag = conditional_line_trend(_df, 0.7)  # 根据线性外推检测函数返回的参数中取
            if reference_tag or line_tag:
                _df = ytd_add(df, data_col)
                pred, pi_pred, pi_dist, mid_data, msg = rule_profit_pipeline(_df, params)
            else:
                # 如果可参考性为true或者（可参考性和线性外推）都为false，执行异常检测并调用api
                params_cad = params_for_cad(data_col)
                _ts = check_pipeline(_df, params_cad)
                #  调用api预测
                pred_pipred = tspred(_ts.data[['Time', 'Data']], data_col, pred_version, image_name)
                log.info("api预测成功")
                # 下面两行的字段名不对指标预测传出来的字段名不是这三个
                pred_col = ['Time', "y_pred"]
                pi_pred_col = ['Time', "y_pred_lower", "y_pred_upper"]
                # 将a拆分为pred和pi_pred
                pred = pred_pipred[pred_col]
                pi_pred = pred_pipred[pi_pred_col]
                pred.rename(columns={"y_pred": "yhat"}, inplace=True)
                pi_pred.rename(columns={"y_pred_lower": "yhat_lower", "y_pred_upper": "yhat_upper"}, inplace=True)
                pi_dist, mid_data, msg = None, {"pi_confidence": 0.95, "eval_result": 0}, None
        if data_col == 'equipXX':  # ---没实现，明天实现 todo
            pred, pi_pred = IncreaseInCycle(pred, pi_pred)
    except Exception as e:
        log.error("参数：{}，时序长度：{},异常：{}".format(data_col, len(df), e))
        # 错误信息获取添加
        log.error("预测报错-image_name:{}".format(image_name))
        _df = ytd_add(df, data_col)
        pred, pi_pred, pi_dist, mid_data, msg = rule_profit_pipeline(_df, params)
        _df['error'] = str(e)
        n_len = _df['Time'].nunique()
        _df.to_csv(os.path.join(os.path.dirname(__file__),"exception_file", f"{data_col}_{len(_df)}_{n_len}.csv"))
        log.error("兜底执行成功：时序长度类型：{}".format('Long' if n_len >= 11 else 'Short'))

    if pred is not None:
        pred.fillna(fill_value, inplace=True)  # 兜底
    # 异常处理
    return pred, pi_pred, pi_dist, mid_data, msg


def process_dimension(
        dim_key: Tuple[Any],
        dim: List[str],
        pred_cols: List[str],
        period_col: str,
        sub_df: pd.DataFrame,
        pred_version: int
        ) -> List[Dict[str, Any]]:
    """
    处理单个维度组合的所有预测列
    """
    result_rows = []
    result_row = {d: v for d, v in zip(dim, dim_key)}  # 构建维度字段

    for pred_col in pred_cols:
        try:
            # 调用预测函数
            pred, pi_pred, pi_dist, mid_data, msg = process_row_api(
                sub_df,
                period_col=period_col,
                data_col=pred_col,
                image_name='-'.join(map(str, dim_key)) + pred_col, pred_version=pred_version
            )
            # pred, pi_pred, pi_dist, mid_data, msg = process_row(
            #     sub_df,
            #     period_col=period_col,
            #     data_col=pred_col,
            #     image_name='-'.join(map(str, dim_key)) + pred_col, pred_version=pred_version
            # )

            pi_confidence = mid_data.get('pi_confidence', 0.95)
            eval_result = mid_data.get('eval_result', 0)

            # 重置索引
            pred = pred.reset_index()
            pi_pred = pi_pred.reset_index()
            pi_pred.rename(columns={'Time': 'target_period'}, inplace=True)
            pred.rename(columns={'Time': 'target_period'}, inplace=True)
            # 合并点预测和区间预测
            pred_ts = pred.merge(pi_pred, on=period_col, how='left')
            # 处理结果行
            if pred_col in indicators_need_interval_prediction:
                if not pred_ts.empty:
                    for _, time_row in pred_ts.iterrows():
                        temp = result_row.copy()
                        temp.update({
                            period_col: time_row[period_col],
                            f"{pred_col}_fcst": time_row['yhat'],
                            f"{pred_col}_fcst_upper": time_row.get('yhat_upper', None),
                            f"{pred_col}_fcst_lower": time_row.get('yhat_lower', None),
                            f"{pred_col}_pi_dist": pi_dist,
                            f"{pred_col}_fcst_conf": pi_confidence,
                            f"{pred_col}_fcst_acc": eval_result,
                            'msg': msg
                        })
                        result_rows.append(temp)
                else:
                    # 处理空数据情况
                    temp = result_row.copy()
                    temp.update({
                        period_col: None,
                        f"{pred_col}_fcst": None,
                        f"{pred_col}_fcst_upper": None,
                        f"{pred_col}_fcst_lower": None,
                        f"{pred_col}_pi_dist": None,
                        f"{pred_col}_fcst_conf": None,
                        f"{pred_col}_fcst_acc": None,
                        'msg': None
                    })
                    result_rows.append(temp)
            else:
                if not pred_ts.empty:
                    for _, time_row in pred_ts.iterrows():
                        temp = result_row.copy()
                        temp.update({
                            period_col: time_row[period_col],
                            f"{pred_col}_fcst": time_row['yhat'],
                            f"{pred_col}_pi_dist": pi_dist,
                            'msg': msg
                        })
                        result_rows.append(temp)
                else:
                    temp = result_row.copy()
                    temp.update({
                        period_col: None,
                        f"{pred_col}_fcst": None,
                        f"{pred_col}_pi_dist": None,
                        'msg': None
                    })
                    result_rows.append(temp)
        except Exception as e:
            log.error(f"处理 {dim_key} 的 {pred_col} 时出错: {e}")
            # 添加错误标记行
            temp = result_row.copy()
            temp.update({
                period_col: None,
                f"{pred_col}_fcst": None,
                f"{pred_col}_fcst_upper": None,
                f"{pred_col}_fcst_lower": None,
                f"{pred_col}_pi_dist": None,
                f"{pred_col}_fcst_conf": None,
                f"{pred_col}_fcst_acc": None,
                'msg': f"Error: {str(e)}"
            })
            result_rows.append(temp)
    return result_rows


def integrate_results(
        his_df: pd.DataFrame,
        dim: List[str],
        pred_cols: List[str],
        period_col: str,
        pred_version: int
) -> pd.DataFrame:
    """
    整合所有预测结果到 DataFrame
    """
    # 创建维度分组字典
    dim_dict = {tuple(k): v for k, v in his_df.groupby(dim)}

    # 提取唯一的维度组合并过滤掉 NaN
    pred_df = his_df[dim].drop_duplicates()
    pred_df = pred_df.dropna(subset=[dim[-1]])  # 假设最后一个维度不能为 NaN

    # 准备多进程任务参数
    tasks = []
    for _, row in pred_df.iterrows():
        dim_key = tuple(row[d] for d in dim)
        sub_df = dim_dict.get(dim_key, pd.DataFrame())
        tasks.append((dim_key, dim, pred_cols, period_col, sub_df, pred_version))

    # results = [process_dimension(*params) for params in tasks]
    # # 根据 CPU 核心数设置进程池大小
    concurrent_num = os.environ.get('PROCESS_NUM', 8)
    with Pool(processes=int(concurrent_num)) as pool:
        results = pool.starmap(process_dimension, tasks)

    # 合并所有结果
    main_result = pd.DataFrame([item for sublist in results for item in sublist])

    return main_result


def is_monthly_continuous(df_train, df_test):
     # 确保日期列是 datetime 类型
    df = pd.concat([df_train, df_test], axis=0)
    date_col = "Time"
    # 提取年月（Period 类型）
    months = df[date_col].dt.to_period('M')
    # 获取最小和最大日期
    start = df[date_col].min()
    end = df[date_col].max()
    # 生成完整的月份序列（从 start 到 end，每月初）
    full_months = pd.date_range(start=start, end=end, freq='MS').to_period('M')
    # 检查集合是否一致
    return set(months) == set(full_months)

