{"DEPLOY": {"cloud": "HIS", "infra": "AIF", "enable_doc": true, "service_name": "/fcst/profits", "uri_prefix": "/fcst/profits", "config_server": {"url": "http://appconfig-beta.huawei.com/ConfigCenter/services/saasConfigcenterGetConfig", "app_id": "com.huawei.finance.ai.opt.fop", "du": "fcst_profits_service", "environment": "kwe_uat", "region": "cn-west-hcd-1", "version": "1.0", "config_parts": ["c6945803503046b49385d6bfbbffc47d", "5d63a4c794674dfd853574cbc01a9888"]}, "api_gateway": {"authorization": {"url": "http://kwe-beta.huawei.com/ApiCommonQuery/appToken/getRestAppDynamicToken"}, "authentication": {"sgov_token": "http://kwe-beta.huawei.com/ApiCommonQuery/appToken/validateToken", "sgov_uri": "http://kwe-beta.huawei.com/ApiCommonQuery/appToken/authorizeByURI"}}}, "LOGGER": {"level": "DEBUG"}, "RDB": {"pg_demo": {"datasource_name": "profits_gauss_uat", "tables": ["dm_fop_ict_pl_sum_t"], "echo": true}}, "OBS": {"demo_bucket": {"bucket": "fai-mlops-demo", "pool_size": 5, "max_overflow": 5, "pool_timeout": 2, "pool_recycle": 3600, "pool_pre_ping": true}}, "LIMITER": {"storage_uri": null, "strategy": "moving-window", "unit": "minute", "frequency": 10}}