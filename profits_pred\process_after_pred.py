from datetime import datetime

# dimension_subcategory_pred_df 量纲子类场景
# 先判断结果表df是否有均本>均价的现象
# 同一个period_id的同一个dim里面，如果有这种情况，就把这个period_id 的这个dim的数据用另外的数替换
import pandas as pd

from src.utils.resource_loader import LOGGER as log


# 均本、均价，只涉及到量纲分组和量纲子类的场景
#    * 判断逻辑：同一预测对象，预测有均本>均价的现象(只要有一个点有这种情况就算，比如只有202412一个时间点，均本大于均价，也认为预测的不合理)
#    * 替换逻辑：整条时序预测结果用去年同期结果替换，如预测202407-202412，用202306-202312值作为预测结果


def transform_date(row):
    row_pre = row[:-3]
    row_suffix = row[-3:]
    if not row_suffix.isdigit():
        row = row_pre
    if '-' in row:
        row = row.replace("-", "")
    row = datetime.strptime(row, '%Y%m')
    row = pd.to_datetime(row) + pd.DateOffset(years=1)
    row = row.strftime("%Y%m")
    return row


def group_transform_date(row):
    if type(row) == str:
        #     如果后三字符不是数字，则去掉转为"%Y%m"，如果有横杠则去掉
        if '-' in row:
            row = row.replace("-", "")
    else:
        try:
            row = row.strftime("%Y%m")
        except Exception:
            row = row.strftime("%Y-%m")
            row = row.replace("-", "")

    if len(row) > 6:
        row = row[:6]
    return row


def after_process(pred_df, his_df, groupby_columns):
    grouped = pred_df.groupby(groupby_columns)
    for name, group in grouped:
        try:
            group_inx = group.index
            group_on = groupby_columns + ['target_period']
            if (group['unit_cost_fcst'] > group['unit_price_fcst']).sum() > 0:
                # 获取列和列值
                condition = dict(zip(groupby_columns, name))  # {'dim':A,'period_id':202407}
                query_parts = [f"{k} == {repr(v)}" for k, v in condition.items()]
                query_str = " and ".join(query_parts)
                filtered_data = his_df.query(query_str)
                # target_period用上一年的
                filtered_data.loc[:, 'target_period'] = filtered_data['target_period'].apply(transform_date)
                group.loc[:, 'target_period'] = group['target_period'].apply(group_transform_date)
                filtered_data.drop_duplicates(subset=group_on, keep='first', inplace=True)
                # 已经准备好了filtered_data,按target_period一个一个替换原有的pred_df的unit_cost, unit_price
                merge_df = \
                    pd.merge(group, filtered_data[groupby_columns + ['unit_cost', 'unit_price', 'target_period']],
                             on=group_on, how='inner')[group_on + ['unit_cost', 'unit_price']]
                group = pd.merge(group, merge_df, on=group_on, how='left')
                group.drop_duplicates(subset=group_on, keep='first', inplace=True)
                pred_df.loc[group_inx, ['unit_cost_fcst', 'unit_price_fcst']] = group[
                    ['unit_cost_fcst', 'unit_price_fcst']].values
        except Exception as e:
            log.error("预测后处理参数-{}错误:{}".format(name, e))


def after_process_ratio(pred_df, his_df, groupby_columns, his_param, pre_param):
    grouped = pred_df.groupby(groupby_columns)
    for name, group in grouped:
        try:
            group_inx = group.index
            # 获取去年全年的ratio
            # 获取列和列值
            condition = dict(zip(groupby_columns, name))  # {'dim':A,'period_id':202407}
            query_parts = [f"{k} == {repr(v)}" for k, v in condition.items()]
            query_str = " and ".join(query_parts)
            filtered_data = his_df.query(query_str)
            year_ratio_values = filtered_data[his_param]
            if year_ratio_values.empty:
                year_ratio = 0
            else:
                year_ratio = filtered_data[filtered_data[his_param].notna()][his_param]
                if not year_ratio.empty:
                    year_ratio = max(year_ratio)
                else:
                    year_ratio = 0
            for i, g in group.iterrows():
                if abs(g[pre_param]) > 1:
                    if abs(year_ratio) > 1:
                        # 获取列和列值
                        year_ratio = 0
                        pred_df.loc[group_inx, pre_param] = 0
                    else:
                        pred_df.loc[group_inx, pre_param] = year_ratio
        except Exception as e:
            log.error('参数：{}-分组赋值报错：{}'.format(name, e))
