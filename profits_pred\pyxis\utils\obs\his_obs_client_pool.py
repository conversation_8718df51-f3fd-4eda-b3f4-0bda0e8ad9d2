import json
import logging
import threading
import time
from enum import Enum
from profits_pred.pyxis.utils.obs.his_obs_client import HisObsClient


class HisObsClientPoolErrorMsg(str, Enum):
    POOL_CLOSED = "Pool already closed!"
    POOL_EXHAUSTED = "No available session!"
    POOL_TIMEOUT = "Get session time out!"
    UNRECOGNIZED_SESSION = "Unrecognized session!"


class HisObsClientPoolError(Exception):
    def __init__(self, message: str):
        super().__init__()
        self.message = message

    def to_dict(self):
        return {
            "error_type": self.__class__.__name__,
            "message": self.message
        }

    def __str__(self) -> str:
        return json.dumps(self.to_dict())


class _HisObsClientPool:
    def __init__(
        self,
        pool_size: int = 5,
        max_overflow: int = 5,
        *args, **kwargs
    ):
        self._pool_size = pool_size
        self._max_overflow = max_overflow
        self.closed = False

        self._args = args
        self._kwargs = kwargs

        self._pool = list()
        self._used = dict()
        self._key_map = dict()
        self._keys = 0

        for _ in range(self._pool_size):
            self._connect()

    def _check_pool_closed(self):
        if self.closed:
            raise HisObsClientPoolError(HisObsClientPoolErrorMsg.POOL_CLOSED)

    def _connect(self, key=None):
        session = HisObsClient(*self._args, **self._kwargs)
        if key is not None:
            self._used[key] = session
            self._key_map[id(session)] = key
        else:
            self._pool.append(session)
        return session

    def _get_key(self):
        self._keys += 1
        return self._keys

    def _get_session(self, key=None):
        self._check_pool_closed()

        if key is None:
            key = self._get_key()

        if key in self._used:
            return self._used[key]

        if self._pool:
            self._used[key] = conn = self._pool.pop()
            self._key_map[id(conn)] = key
            return conn
        else:
            if len(self._used) == self._pool_size + self._max_overflow:
                raise HisObsClientPoolError(HisObsClientPoolErrorMsg.POOL_EXHAUSTED)
            return self._connect(key)

    def _put_session(self, session: HisObsClient, key=None, close=False):
        self._check_pool_closed()

        if key is None:
            key = self._key_map.get(id(session))
            if key is None:
                raise HisObsClientPoolError(HisObsClientPoolErrorMsg.UNRECOGNIZED_SESSION)

        if len(self._pool) < self._pool_size + self._max_overflow and not close:
            if session.ping():
                self._pool.append(session)
        else:
            session.release()

        if not self.closed or key in self._used:
            del self._used[key]
            del self._key_map[id(session)]

    def _release(self):
        self._check_pool_closed()

        for session in self._pool + list(self._used.values()):
            try:
                session.release()
            except Exception as e:
                logging.error(e)

        self.closed = True


class HisObsClientPool(_HisObsClientPool):
    def __init__(
        self,
        pool_size: int = 5,
        max_overflow: int = 5,
        pool_timeout: float = 2.0,
        pool_recycle: float = 60.0,
        pool_pre_ping: bool = False,
        *args, **kwargs
    ):
        super().__init__(
            pool_size=pool_size,
            max_overflow=max_overflow,
            *args, **kwargs
        )
        self._pool_timeout = pool_timeout
        self._pool_recycle = pool_recycle
        self._pool_pre_ping = pool_pre_ping
        self._lock = threading.Lock()

    def get_session(self, key=None):
        tick = time.time()

        while True:
            try:
                with self._lock:
                    conn = self._get_session(key)

                if self._pool_pre_ping:
                    if conn.ping():
                        return conn
                    else:
                        self.put_session(conn, key, True)
                else:
                    return conn
            except HisObsClientPoolError as e:
                if e.message == HisObsClientPoolErrorMsg.POOL_CLOSED:
                    raise
                else:
                    pass

            if time.time() - tick >= self._pool_timeout:
                raise HisObsClientPoolError(HisObsClientPoolErrorMsg.POOL_TIMEOUT)
            time.sleep(0.1)

    def put_session(self, session: HisObsClient = None, key=None, close=False):
        with self._lock:
            if time.time() - session.creation_date >= self._pool_recycle:
                self._put_session(session, key, True)
            else:
                self._put_session(session, key, close)

    def release(self):
        with self._lock:
            self._release()
