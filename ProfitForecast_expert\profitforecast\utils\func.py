import copy
import multiprocessing
import time
import warnings
from datetime import datetime
from typing import Dict, List, Tuple

import numpy as np
import pandas as pd
from aipaas.logger_factory import logger

warnings.filterwarnings('ignore')

PI_EVAL_METRIC = ["picp", "pinaw", "cwc"]


def division_protection(data_1, data_2):
    """除法保护

    Parameters
    ----------
    data_1: 分子
    data_2: 分母

    Returns
    -------
    float 除法结果
    """
    return np.true_divide(data_1, data_2)


class DataBuild:
    """数据预处理逻辑组件

    Parameters
    ----------
    data:预处理数据，长表
    args：参数

    """

    def __init__(self, data, **args):
        self.data = data
        self.target_name = args.get("target_name")
        self.month_flag = args.get("month_flag")
        self.ytd_flag = args.get("ytd_flag")
        self.method = args.get("method")
        self.miss_threshold = args.get("threshold")
        self.date_time = args.get("time")
        self.train = pd.DataFrame()
        self.ytd_data = args.get("res_ytd")

    def make_ytd_data(self):
        """原始的YTD数据可能会存在数据缺失的情况，比如少了中间一整年，导致后面取发货量和设备收入出错，这里补全时间列

        Returns
        -------
        dataframe
        """
        time_data = make_date(self.date_time)
        empty_data = pd.DataFrame()
        for i in self.ytd_data["unite"].unique().tolist():
            df = self.ytd_data[self.ytd_data["unite"] == i]
            df = pd.merge(time_data, df, how="left", on="period_id")
            df["unite"] = i
            empty_data = pd.concat([empty_data, df])
        self.ytd_data = empty_data

    def sum_miss_ratio(self, data_col):
        """计算最近两年数据的缺失率

        Parameters
        ----------
        data_col: 待处理列

        Returns
        -------
        bool
        """
        df = self.train.iloc[self.train[data_col].first_valid_index():]
        df_copy = df.iloc[-24:]
        missing_ratio = division_protection(df_copy[data_col].isnull().sum(), df_copy.shape[0])
        return "True" if missing_ratio >= self.miss_threshold else "False"

    def get_last_year_ytd(self, col_name):
        """计算时序准入规则需要填充的数

        Parameters
        ----------
        col_name: 待计算指标

        Returns
        -------
        float 填充数
        """
        df = self.ytd_data[self.ytd_data["unite"] == col_name]
        now_time = self.data["period_id"].max()
        period_time = str(self.date_time)[:4]
        last_year = str(int(period_time) - 1) + "-12"
        equip_amt, spart_qty = self.get_last_year_num(last_year, df)
        nums_last = division_protection(equip_amt, spart_qty)
        if self.method == "last":
            return nums_last if (
                not str(nums_last) == "nan" and not str(abs(nums_last)) == "inf") else 0, nums_last if (
                not str(nums_last) == "nan" and not str(abs(nums_last)) == "inf") else 0

        if self.method == "all":
            equip_amt1, spart_qty1 = self.get_last_year_num(last_year, df)
            equip_amt2, spart_qty2 = self.get_last_year_num(now_time, df)
            nums = division_protection(equip_amt1 + equip_amt2, spart_qty1 + spart_qty2)
            return nums if (not str(nums) == "nan" and not str(abs(nums)) == "inf") else 0, nums_last if (
                not str(nums_last) == "nan" and not str(abs(nums_last)) == "inf") else 0
        return 0, 0

    def get_last_year_num(self, target_time, df):
        """从YTD数据拿出累计的对价前设备收入和发货量

        Parameters
        ----------
        target_time: 要取得时间，时去年12月还是今年累计
        df: YTD数据

        Returns
        -------
        float、float
        """
        try:
            equip_amt, spart_qty = 0., 0.
            target_df = df[df["period_id"] == target_time]
            spart_qty = float(target_df["spart_qty"].values.tolist()[0]) if \
                target_df.shape[0] > 0 else float("nan")

            equip_amt, spart_qty = self.get_spart_amt(equip_amt, spart_qty, target_df)

        except Exception as e:
            logger.info(f"unite:{df.unite.unique().tolist()}, target_time: {target_time}\n异常信息:{e}")
            raise e
        return equip_amt if (not str(equip_amt) == "nan" and not str(abs(equip_amt)) == "inf") else 0, spart_qty if (
            not str(spart_qty) == "nan" and not str(abs(spart_qty)) == "inf") else 0

    def get_spart_amt(self, equip_amt, spart_qty, target_df):
        """
        根据指标名称`self.target_name`获取对应的对价前设备收入和发货量
        """
        if self.target_name == "unit_cost":
            equip_amt = float(target_df["equip_cost_cons_before_amt"].values.tolist()[0]) if \
                target_df.shape[0] > 0 else float("nan")
        if self.target_name == "unit_price":
            equip_amt = float(target_df["equip_rev_cons_before_amt"].values.tolist()[0]) if \
                target_df.shape[0] > 0 else float("nan")
        if self.target_name == "mgp_adjust_rate":
            equip_amt = float(target_df['mgp_adjust_rate'].values.tolist()[0]) if \
                target_df.shape[0] > 0 else float("nan")
            spart_qty = 1
        if self.target_name == "mca_adjust_rate":
            equip_amt = float(target_df['mca_adjust_rate'].values.tolist()[0]) if \
                target_df.shape[0] > 0 else float("nan")
            spart_qty = 1
        if self.target_name == "carryover_rate":
            equip_amt = float(target_df['carryover_rate'].values.tolist()[0]) if \
                target_df.shape[0] > 0 else float("nan")
            spart_qty = 1
        return equip_amt, spart_qty

    def get_train(self):
        """得到训练数据

        Returns
        -------
        train:dataframe
        """
        train = make_date(self.date_time)
        for i in self.data["unite"].unique():
            unite_data = self.data[self.data["unite"] == i].reset_index(drop=True)[["period_id", self.target_name]]
            unite_data.columns = ["period_id", i]
            train = pd.merge(train, unite_data, on="period_id", how="left")
        return train

    def data_preprocess(self, df: pd.DataFrame):
        """时序数据预处理

        Parameters
        ----------
        df: dataframe

        Returns
        -------
        dataframe
        """
        df_copy = copy.deepcopy(df)
        df_res = df_copy[df_copy.columns[0]]
        for col_name in df_copy.columns[1:]:
            df_copy[col_name] = df_copy[col_name].apply(lambda x: np.nan if x == float("inf") else x)
            df_copy[col_name] = df_copy[col_name].apply(lambda x: np.nan if x == float("-inf") else x)
            if self.month_flag:  # 月度数据预处理
                after_preprocess_data = datapreprocess_month(
                    df_copy[[df_copy.columns[0], col_name]], col_name,
                    self.target_name
                )
            else:  # 年度数据预处理
                after_preprocess_data = datapreprocess_year(
                    df_copy[[df_copy.columns[0], col_name]], col_name,
                    self.target_name
                )
            df_res = pd.merge(df_res, after_preprocess_data, how="left", on=df_copy.columns[0])
        return df_res

    def get_data(self, data, column):
        """只保留模型预测的三列数据(Time, Data, UNION_ATTR)

        Parameters
        ----------
        data: 原数据
        column: Data列所在列名

        Returns
        -------
        dataframe
        """
        test = data[["period_id", column]]
        test["UNION_ATTR"] = column + "_" + self.target_name
        test.columns = ["Time", "Data", "UNION_ATTR"]
        test = test.iloc[test["Data"].first_valid_index():].reset_index(drop=True)
        test["Miss"] = self.sum_miss_ratio(column)
        if self.target_name in ["unit_cost", "unit_price"]:
            test["ytd_data"], test["fill_data"] = self.get_last_year_ytd(column)
        if self.target_name in ["mgp_adjust_rate", "mca_adjust_rate", "carryover_rate"] and self.ytd_flag:
            test["ytd_data"], test["fill_data"] = self.get_last_year_ytd(column)
        return test

    def transform(self):
        """

        Returns
        -------
        chunks：list[dataframe, dataframe,...]
        """
        self.make_ytd_data()
        self.train = self.get_train()
        multi_ts_df = self.data_preprocess(self.train)
        chunks = [self.get_data(multi_ts_df, col_name) for col_name in multi_ts_df.columns[1:]]
        return chunks


def make_date(date_time):
    """构造时间列，用于填充一些数据存在时间缺失

    Parameters
    ----------
    date_time: int or str 会计期  "202306"

    Returns
    -------
    dataframe
    """
    start_date = datetime(2019, 1, 1)  # 设置开始日期
    end_date = datetime(int(str(date_time)[:4]), int(str(date_time)[-2:]), 1)  # 设置结束日期
    dates = []

    for year_month in range(start_date.year * 12 + start_date.month - 1, end_date.year * 12 + end_date.month - 1):
        year, month = divmod(year_month, 12)
        dates.append(datetime(year, month + 1, 1).strftime('%Y-%m'))

    return pd.DataFrame(dates, columns=["period_id"])


def datapreprocess_month(df, column, name):
    """分月数据的数据预处理模块

    Parameters
    ----------
    df: 待处理数据
    column: 需要处理的列
    name: 指标名称

    Returns
    -------
    dataframe
    """
    data = copy.deepcopy(df)
    data[column] = data[column].apply(lambda x: np.nan if x == -999999 else x)
    data = data.iloc[data[column].first_valid_index():]
    data = data_fill_method(column, data, df, name)
    return data


def data_fill_method(column, data, data1, name):
    """数据填充逻辑

    Parameters
    ----------
    column: 需要处理的列
    data: 待处理数据
    data1: 待处理数据
    name: 指标名称

    Returns
    -------
    dataframe
    """
    length = len(data)
    if not length:
        data = FillMissingValueTransformer(method="zero", data_col=column).fit_transform(data1)
    # 第一类指标填充方式
    if name in ["unit_cost", "unit_price", "mgp_adjust_rate", "mca_adjust_rate", "mgp_rate", "carryover_rate"]:
        if len(data[data[column].isna()]) == data.shape[0]:
            data = FillMissingValueTransformer(method="zero", data_col=column).fit_transform(data)
        else:
            data = FillMissingValueTransformer(method="forward", data_col=column).fit_transform(data)
            data = data.dropna().reset_index(drop=True)
    # 第二类指标填充方式
    elif name in ["equip_rev_cons_after_amt", "rev_percent"]:
        data = FillMissingValueTransformer(method="zero", data_col=column).fit_transform(data)
    return data


def datapreprocess_year(df, column, name):
    """数据预处理模块

    Parameters
    ----------
    df: 待处理数据
    column: 需要处理的列
    name: 指标名称

    Returns
    -------
    dataframe
    """
    data = copy.deepcopy(df)
    data[column] = data[column].apply(lambda x: np.nan if x == -999999 else x)
    data = FillMissingValueTransformer(method="back", data_col=column).fit_transform(data)
    data = data_fill_method(column, data, df, name)
    return data


def get_model_result(
    ts_df: pd.DataFrame,
    target_list: List[str],
    steps: int,
    date_time: str,
    kwargs: Dict
) -> Tuple[pd.DataFrame, Dict]:
    """获取时序模型的评估指标和最终预测结果

    Parameters
    ----------
    ts_df: 待预测数据，长表，需要转换为一个时序一列的宽表
    target_list: 待预测指标list
    steps: 预测步长
    date_time: 会计期
    kwargs: 其它参数

    Returns
    -------
    dataframe(预测结果)
    dict(错误dict、点预测评估、区间预测评估、分布结果)
    """
    all_pred_df = pd.DataFrame()
    all_train_test_df = pd.DataFrame()
    all_error_dict = {}  # 错误信息
    all_metric_dict = {}  # 点预测评估
    all_pi_metric_dict = {}  # 区间预测评估
    all_distribution_dict = {}  # 分布结果
    ytd_flag = kwargs.get("ytd_flag", False)
    kwargs["method"] = "last"
    kwargs["threshold"] = 0.5
    kwargs["time"] = date_time
    for target_name in target_list:
        logger.info(f"开始处理指标：{target_name},时间：{time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())}")
        kwargs["target_name"] = target_name
        preprocess_model = DataBuild(ts_df, **kwargs)
        chunks = preprocess_model.transform()
        multi_ts_df = preprocess_model.train
        # 获取任务参数
        params = init_params(steps, target_name, ytd_flag, kwargs)
        # 多进程并行执行任务
        task_num = multiprocessing.cpu_count() - 1
        logger.info(f"线程数:{task_num}")
        pool = MultiprocessPool(task_num, "Time Series Model Train and Predict Task")

        if target_name == "equip_rev_cons_after_amt":  # 不做异常检测
            params["anomaly_entry_conditions"] = ""
        func_params = {"params": params}

        chunk_size = multi_ts_df.shape[1] - 1
        res_list = pool.submit(rule_profit_pipeline, func_params, generator(chunks), chunk_size)

        # 处理多进程任务结果
        _tmp_params = (all_distribution_dict, all_error_dict,
        all_metric_dict, all_pi_metric_dict,
        all_pred_df, multi_ts_df, params,
        res_list, target_name, all_train_test_df)
        all_pred_df, all_train_test_df = handle_task_result(_tmp_params)
        logger.info(f"指标{target_name}处理结束,时间：{time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())}")
    all_metric_dict = {
        "metric": all_metric_dict,
        "error": all_error_dict,
        "pi_metric": all_pi_metric_dict,
        "distribution": all_distribution_dict,
        "train_test_df": all_train_test_df,
    }

    return all_pred_df, all_metric_dict


def handle_task_result(input_params):
    """处理并行任务返回结果

    Parameters
    ----------
    input_params: 模型参数

    Returns
    -------
    dataframe(预测结果)
    """
    all_distribution_dict, all_error_dict, all_metric_dict, \
        all_pi_metric_dict, all_pred_df, multi_ts_df, params, \
        res_list, target_name, all_train_test_df = input_params
    tmp_pred_df, metric_dict, pi_metric_dict, distribution_dict = pd.DataFrame(), {}, {}, {}

    for idx, res in enumerate(res_list):
        pred_df, pi_pred_df, distribution, middle_result, msg = res
        if pred_df is None:
            logger.error(msg)
            continue
        is_bottom = middle_result.get("is_bottom")
        col_name = multi_ts_df.columns[idx + 1]
        fcst_acc = " "
        if middle_result.get("ensemble_eval_result") is not None:
            fcst_acc = 1 - middle_result.get(
                "ensemble_eval_result"
            )  # 1-mape or 1-mae
        param_dict = {
            "target_name": target_name,
            "is_bottom": is_bottom,
            "confidence": params.get("confidence"),
            "models": params.get("models"),
            "union_attr": col_name,
            "fcst_acc": fcst_acc,
            "eval_result": middle_result.get("eval_result"),
            "pi_eval_result": middle_result.get("pi_eval_result"),
        }
        pred_res, metric_args = format_pred_eval_result(
            pred_df, pi_pred_df,
            param_dict
        )
        metric = metric_args.get("metric")
        pi_metric = metric_args.get("pi_metric")

        metric_dict[col_name], pi_metric_dict[col_name], distribution_dict[col_name] = metric, pi_metric, distribution

        # 拼接输入时序(train_df+test_df)
        train_test_df = concat_train_test_df(middle_result.get("train"), middle_result.get("test"))
        tmp_pred_df = pd.concat([tmp_pred_df, pred_res])
        all_train_test_df = pd.concat([all_train_test_df, train_test_df])

    tmp_pred_df = tmp_pred_df.reset_index()

    all_train_test_df = all_train_test_df.reset_index(drop=True)

    if all_pred_df.shape[0] == 0:
        all_pred_df = tmp_pred_df
    else:
        all_pred_df = pd.merge(
            all_pred_df, tmp_pred_df, how="left",
            on=["target_period", "unite"]
        )

    all_error_dict[target_name], all_metric_dict[target_name], all_pi_metric_dict[target_name], all_distribution_dict[
        target_name] = {}, metric_dict, pi_metric_dict, distribution_dict

    return all_pred_df, all_train_test_df


def concat_train_test_df(train_df, test_df):
    """拼接训练集和测试集，输入有可能为None

    Parameters
    ----------
    train_df: 训练集
    test_df: 测试集

    Returns 拼接后的数据，如果输入均为空，返回空的pd.DataFrame对象
    -------
    """
    if train_df is None:
        res = pd.DataFrame()
    elif test_df is None:
        res = train_df
    else:
        res = pd.concat([train_df, test_df])

    return res


def get_save_path(ytd_flag, level, target_name):
    """构造AutoTsPred参数中保存图片的地址

    Parameters
    ----------
    ytd_flag: 是否是YTD
    level: 层级
    target_name: 预测指标名称

    Returns
    -------
    str 保存路径
    """
    level_ytd = "Month"
    if ytd_flag:
        level_ytd = "Ytd"
    return f"./{level_ytd}/{str(level)}/{target_name}/"


def init_params(steps: int, target_name: str, ytd_flag: bool, kwargs: Dict) -> Dict:
    """根据不同指标初始化预测任务参数

    Parameters
    ----------
    steps: 预测步长
    target_name: 预测指标名称
    ytd_flag: 是否是YTD
    kwargs: 其他参数（测试集长度、YTD标记、是否是月度数据、YTD数据）

    Returns
    -------
    dict 预测任务参数
    """
    # metric - 评估指标
    # bayes_p_value - 贝叶斯变点检测p值
    [adjust_tag, anomaly_entry, bayes_p_value, metric, model_nums, seasonal, trend_thold] = model_params_rule(
        kwargs,
        target_name,
        ytd_flag
    )
    if ytd_flag:
        ignore_month = [11, 12]
    else:
        ignore_month = [6, 12]
    params = {
        # 异常检测
        "anomaly_entry_conditions": anomaly_entry,
        "anomaly_model_name": "three_sigma",
        "anomaly_kwargs": {
            "thold": 4,
            "post_process_method": "ignore_anomaly_by_month",  # 指定异常检测后处理方法
            "ignore_month_list": ignore_month,  # 指定要忽略的异常月份
        },
        "reference_detection": True,
        "line_trend_thres": trend_thold,
        "adjust_tag": adjust_tag,
        # 变点检测
        "bayes_p_value": bayes_p_value,  # 贝叶斯变点检测p值
        "is_truncated": True,  # 是否按变点截断数据
        "ignore_last_n": 13,  # 截断数据时忽略最后n个时点中识别出的变点
        # 短时序
        "short_ts_threshold": 5,  # 短时序判断阈值，大于阈值就不是短时序
        # 数据集划分 - 暂时写死
        "fixed_test_size": 6,
        # 模型训练、预测、评估
        "models": model_nums,
        "eval_metric": metric,  # 点预测评估指标
        "confidence": 0.95,  # 区间预测置信度
        "ensemble_method": "weight",
        "plot": None,
        "path": get_save_path(ytd_flag, kwargs.get("level"), target_name),
        "model_params": {
            "arima": {"seasonal": seasonal},
            "naive": {"sp": 12},
            "auto_ets": {
                "seasonal_periods": 12,
                "params":
                    {"yearly_accumulation": True}
            },
            "prophet": {
            },
        },
        "steps": steps,  # 预测步长
        "predict_rule": "fill_data"  # fill_data/last_value/moving_average
    }
    return params


def model_params_rule(kwargs, target_name, ytd_flag):
    trend_thold = 0.8
    # 评估指标和变点检测参数值
    if target_name in ["unit_cost", "unit_price"]:
        metric, bayes_p_value = "mape", 0.05
    elif target_name in ["equip_rev_cons_after_amt"]:
        metric, bayes_p_value = "mape", 0.1
    else:
        metric, bayes_p_value = "mae", 0.01

    # 时序模型
    if target_name in ["unit_price"]:
        model_nums = ["auto_ets", "prophet", "naive"]  # "arima",
    else:
        model_nums = ["arima", "auto_ets", "prophet", "naive"]
    #
    # 季节性和变点检测标记
    if target_name in ["unit_cost", "unit_price"]:
        seasonal = False
    else:
        seasonal = False
    if target_name in ["equip_rev_cons_after_amt"] and ytd_flag:
        adjust_tag = True
    else:
        adjust_tag = False

    # 异常检测参数
    if target_name in ["equip_rev_cons_after_amt",
        "mca_adjust_rate",
        "mgp_adjust_rate",
        "carryover_rate"
    ]:
        anomaly_entry = "not_bayes_cycle_test"
        trend_thold = 0.65

    else:
        anomaly_entry = "bayes_cycle_test"

    # 变点检测参数
    if target_name in ["mca_adjust_rate",
        "mgp_adjust_rate",
        "unit_price",
        "unit_cost",
        "carryover_rate",
        "rev_percent"]:
        bayes_p_value = 1.1

    kwargs["metric"] = metric

    return [adjust_tag, anomaly_entry, bayes_p_value, metric, model_nums, seasonal, trend_thold]


def format_pred_eval_result(
    pred_df: pd.DataFrame,
    pi_pred_df: pd.DataFrame,
    params: Dict,
) -> Tuple[pd.DataFrame, Dict]:
    """按照业务要求格式化预测和评估结果

    Parameters
    ----------
    pred_df: 点预测结果
    pi_pred_df: 区间预测结果
    params: 其它参数

    Returns
    -------
    dataframe（预测结果）、dict（评价指标）
    """
    target_name = params.get("target_name")
    is_bottom = params.get("is_bottom")
    confidence = params.get("confidence")
    models = params.get("models")
    union_attr = params.get("union_attr")
    fcst_acc = params.get("fcst_acc")
    eval_result = params.get("eval_result")
    pi_eval_result = params.get("pi_eval_result")

    if any(e is None for e in [target_name, is_bottom, confidence, models, union_attr, fcst_acc]):
        raise Exception(f"[{format_pred_eval_result.__name__}]必须参数为空，请检查")

    merged_df = pred_df.join(pi_pred_df)
    merged_df.index.name = "target_period"  # 后续指定该列名做数据拼接条件
    for i in ["yhat", "yhat_lower", "yhat_upper"]:
        merged_df[i] = merged_df[i].apply(lambda x: round(x, 10))

    merged_df = merged_df.rename(
        columns={
            "yhat_lower": target_name + "_fcst_lower",
            "yhat_upper": target_name + "_fcst_upper",
            "yhat": target_name + "_fcst",
        }
    )
    merged_df[target_name + "_fcst_conf"] = confidence
    merged_df[target_name + "_fcst_acc"] = fcst_acc
    merged_df["unite"] = union_attr

    if is_bottom or len(eval_result) == 0:  # 兜底逻辑和部分短时序预测,没有评价指标
        short_ts_msg = " "
        metric_dict = dict(zip(models, [short_ts_msg] * len(models)))
        tmp_pi_metric = dict(zip(PI_EVAL_METRIC, [short_ts_msg] * len(PI_EVAL_METRIC)))
        pi_metric_dict = dict(zip(models, [tmp_pi_metric] * len(models)))
    else:
        key_ = list(eval_result.keys())[0]
        value_ = list(eval_result.get(key_).values())[0]  # 将嵌套的dict值取出
        metric_dict = {key_: value_}
        pi_metric_dict = pi_eval_result
    eval_metric = {
        "metric": metric_dict,
        "pi_metric": pi_metric_dict,
    }

    return merged_df, eval_metric


def split_concat(target_name, train_data):
    """按bg合并lv1指定产业的子段

    Parameters
    ----------
    target_name: 需要合并的字段名称
    train_data: 原始数据

    Returns
    -------
    dataframe
    """
    target_level = train_data["oversea_desc"].unique().tolist()[0]
    data = pd.DataFrame()
    for i in target_name:
        data = pd.concat([data, train_data[train_data["lv1_name"] == i]]).reset_index(drop=True)
    columns = data["bg_name"].unique().tolist()
    unite_target = pd.DataFrame()
    for i in columns:
        new_data = data[data["bg_name"] == i].groupby(["period_id"]).agg(sum).reset_index()
        new_data["mgp_rate"] = 1 - division_protection(
            new_data["equip_cost_cons_after_amt"],
            new_data["equip_rev_cons_after_amt"]
        )
        new_data["bg_name"] = i
        new_data["lv1_code"] = "合并"
        new_data["lv1_name"] = "合并"
        new_data["oversea_desc"] = target_level
        new_data["currency"] = "CNY"
        new_data["unite"] = i + "_合并"
        new_data["bg_code"] = data[data["bg_name"] == i]["bg_code"].iloc[0]
        length = len(unite_target)
        if not length:
            unite_target = new_data
        else:
            unite_target = pd.concat([unite_target, new_data])
    return unite_target


def get_unite(dist_data):
    """生成区间分布预测数据的公共列

    Parameters
    ----------
    dist_data:原始数据宽表，只有一个unite

    Returns
    -------
    dataframe 公共列
    """
    unite_dict = {}
    if "l2_name" in dist_data.columns:
        columns = ["bg_code", "bg_name", "oversea_desc", "lv1_code", "lv1_name", "lv2_code", "lv2_name", "l1_name",
            "l2_name", "currency", "articulation_flag"]
    elif "l1_name" in dist_data.columns and "l2_name" not in dist_data.columns:
        columns = ["bg_code", "bg_name", "oversea_desc", "lv1_code", "lv1_name", "lv2_code", "lv2_name", "l1_name",
            "currency", "articulation_flag"]
    else:
        columns = ["bg_code", "bg_name", "oversea_desc", "lv1_code", "lv1_name", "currency"]
    for i in columns:
        try:
            unite_dict[i] = dist_data[i].iloc[0]
        except Exception as e:
            logger.info(f"错误原因：{e}\ncolumns:{columns},dist_data:{dist_data}")
            continue
    return unite_dict


def get_distribution(train_data, distribution_orl, data_res):
    """根据dict生成可以区间分布数据

    Parameters
    ----------
    train_data: 原始数据宽表
    distribution_orl: 分布数据的dict
    data_res: 点预测结果（主要为了得到预测的target_period，并转成列）

    Returns
    -------
    dataframe
    """
    columns = data_res["target_period"].unique().tolist()
    distribution = pd.DataFrame()
    for i in distribution_orl.keys():
        for j in tqdm(distribution_orl[i].keys()):
            distribution_data = pd.DataFrame(distribution_orl[i][j].T, columns=columns)
            dist_data = train_data[train_data["unite"] == j]
            unite_dict = get_unite(dist_data)
            for z in unite_dict:
                distribution_data[z] = unite_dict[z]
            distribution_data["type"] = i
            distribution = pd.concat([distribution, distribution_data])
    return distribution


def get_other_columns(df):
    """构造存入数据库的其他标记列

    Parameters
    ----------
    df: 预测结果长表

    Returns
    -------
    dataframe
    """
    df["remark"] = None
    df["created_by"] = -1
    df["creation_date"] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
    df["last_updated_by"] = -1
    df["last_update_date"] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
    df["del_flag"] = "N"
    df["currency"] = 'CNY'
    df["fcst_type"] = 'YTD法'
    df['aggregate_flag'] = None
    return df


def replace_rate(data_concat):
    """因为fop表有的制毛字段是rate,有的是ratio,这里读数时统一成rate处理

    Parameters
    ----------
    data_concat: fop表读取的原始数据

    Returns
    -------
    dataframe 处理后数据
    """
    column_list = []
    for i in data_concat.columns:
        if "rate" in i:
            i = i.replace("rate", "ratio")
        column_list.append(i)
    return column_list


def ytd(data, year, preprocess_col):
    """构建YTD数据时，需要过滤掉脏数据

    Parameters
    ----------
    data: 原始数据
    year: 处理年份
    preprocess_col: list 需要处理的指标字段

    Returns
    -------
    dataframe 处理后数据
    """
    ytd_res = pd.DataFrame()
    year_1 = str(year) + "-01"
    year_2 = str(year) + "-12"
    year_start = year * 100 + 1
    year_end = year * 100 + 13
    data_year = data.query('@year_1 <= period_id <= @year_2').reset_index(drop=True)
    for unite_name in data_year['unite'].unique():
        data_unite = data_year.query('unite==@unite_name').reset_index(drop=True)
        time_index = pd.DataFrame(
            [str(i)[:4] + "-" + str(i)[-2:] for i in range(year_start, year_end)],
            columns=["period_id"]
        ).astype(str)
        new_data = pd.merge(time_index, data_unite, on="period_id", how='left').fillna(0)
        new_data['unite'] = data_unite['unite'].iloc[0]
        tmp_data = pd.DataFrame()
        for i in range(1, new_data.shape[0] + 1):
            new_data_col = new_data[preprocess_col]
            new_data_sum = new_data_col.iloc[:i].sum()
            tmp_data = pd.concat([tmp_data, new_data_sum], axis=1)
        tmp_data = tmp_data.transpose().reset_index(drop=True)
        tmp_data['unite'] = unite_name
        tmp_data['period_id'] = new_data['period_id']
        columns = ['period_id'] + preprocess_col + ['unite']
        tmp_data = tmp_data[columns].reset_index(drop=True)
        ytd_res = pd.concat([ytd_res, tmp_data], axis=0).reset_index(drop=True)
    return ytd_res


def data_load(data, year, col):
    """构造YTD数据的预处理

    Parameters
    ----------
    data: 清洗后的月度数据
    year：需要计算的年度
    col

    Returns
    -------
    dataframe 按year汇总的月度数据
    """
    year_1 = str(year * 100)
    year_2 = str((year + 1) * 100)
    data.replace([np.inf, -np.inf], np.nan, inplace=True)  # 将正负无穷替换为nan
    df_year = data.query('@year_1<period_id<@year_2')  # 2021
    for i in data['unite'].unique():
        if i not in df_year['unite'].unique():
            tmp_data = data[data['unite'] == i]
            tmp_data[col] = 0
            df_year = pd.concat([df_year, tmp_data])
    return df_year


def get_metric_data(metric):
    """记录点预测评价指标

    Parameters
    ----------
    metric: dict 评价指标

    Returns
    -------
    dict
    """
    metric_list = {}
    try:
        for i in metric["eval_result"].keys():
            metric_list[i] = metric["eval_result"][i].iloc[0]
    except Exception as e:
        metric_list["prophet"] = " "
        metric_list["arima"] = " "
        metric_list["auto_ets"] = " "
    return metric_list


def get_pi_metric_data(train_stage_data):
    """记录区间预测评价指标

    Parameters
    ----------
    train_stage_data: dict 预测指标

    Returns
    -------
    dict
    """
    pi_eval = {}
    try:
        pi_eval = train_stage_data["pi_eval_result"]
    except Exception as e:
        pi_eval["prophet"] = {
            "picp": "短时序，无评价指标",
            "pinaw": "短时序，无评价指标",
            "cwc": "短时序，无评价指标"
        }
    return pi_eval
