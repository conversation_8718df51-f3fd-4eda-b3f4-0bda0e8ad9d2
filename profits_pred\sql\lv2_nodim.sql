select
    version_code,
 scenarios,
 time_window_code,
 period_id,
 target_period,
 bg_code,
 bg_name,
 oversea_code,
 oversea_desc,
 lv1_code,
 lv1_name,
 lv2_code,
 lv2_name,
 currency,
 equip_rev_cons_before_amt,
 equip_cost_cons_before_amt,
 equip_rev_cons_after_amt,
 equip_cost_cons_after_amt,
 mgp_ratio_after,
 mgp_ratio_before,
 mca_adjust_ratio,
 mgp_adjust_ratio,
 equip_rev_cons_after_amt_ytd_data,
 mgp_ratio_after_ytd_data,
 mca_adjust_ratio_ytd_data,
 mgp_adjust_ratio_ytd_data,
 remark,
 created_by,
 creation_date,
 last_updated_by,
 last_update_date,
 del_flag
from
dm_fop_dimension_lv2_tgt_period_filled_t
where
left (target_period,6) < {pred_version}
and del_flag = 'N'
and currency = 'CNY'
and bg_name in ('运营商','政企')
and version_code = (select version_code
 from fin_dm_opt_fop.dm_fop_dimension_version_t
 where step = 1
 order by last_update_date desc
 limit 1)
and oversea_desc in ('国内','海外','全球')