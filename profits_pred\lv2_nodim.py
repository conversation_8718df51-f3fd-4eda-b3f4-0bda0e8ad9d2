import os

from db.get_data import get_data
from db.save_to_db import save_to_db
from profits_sequence_pred import integrate_results
from datetime import datetime
from process_after_pred import after_process_ratio
from dimension_constant import lv2_dim, check_input

# 预测因子
lv2_withdim_pred_lst = ['mgp_adjust_ratio', 'mca_adjust_ratio']
lv2_nondim_pred_lst = ['mgp_ratio_after', 'equip_rev_cons_after_amt']
# 区间预测
dim_need_interval_prediction = ['unit_cost', 'unit_price']
lv2nodim_need_interval_prediction = ['mgp_ratio_after', 'equip_rev_cons_after_amt']
cols_to_check = lv2_withdim_pred_lst+lv2_nondim_pred_lst


def profit_forecast(pred_version: int):
    with open(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'sql', 'lv2_nodim.sql'), 'r+',
                  encoding='utf-8') as f:
        lv2_nodim_sql = f.read()
        lv2_nodim_sql = lv2_nodim_sql.format(pred_version=pred_version)
    df = get_data(lv2_nodim_sql)
    # 检查结果
    check_input(df, cols_to_check)

    # 无量纲LV2
    lv2_nodim_his_df = df[(df['scenarios'] == 'LV2')]

    lv2_nodim_pred_df = integrate_results(his_df=lv2_nodim_his_df,
                                          dim=lv2_dim, pred_cols=lv2_nondim_pred_lst,
                                          period_col='target_period', pred_version=pred_version)
    df = lv2_nodim_pred_df.groupby(lv2_dim + ['target_period']).agg(
        {i + '_fcst': 'max' for i in lv2_nondim_pred_lst} |
        {i + '_fcst_upper': 'max' for i in lv2nodim_need_interval_prediction} |
        {i + '_fcst_lower': 'max' for i in lv2nodim_need_interval_prediction} |
        {i + '_fcst_conf': 'max' for i in lv2nodim_need_interval_prediction}
    ).reset_index()
    df['period_id'] = pred_version
    after_process_ratio(df, lv2_nodim_his_df, lv2_dim, his_param='mgp_ratio_after_ytd_data',
                        pre_param='mgp_ratio_after_fcst')
    # 存在未合并的数据，二次合并
    if df.isnull().any().any():
        df = df.groupby(lv2_dim + ['target_period']).agg(
            {i + '_fcst': 'max' for i in lv2_nondim_pred_lst} |
            {i + '_fcst_upper': 'max' for i in lv2nodim_need_interval_prediction} |
            {i + '_fcst_lower': 'max' for i in lv2nodim_need_interval_prediction} |
            {i + '_fcst_conf': 'max' for i in lv2nodim_need_interval_prediction}
            ).reset_index()

    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    df.to_csv(os.path.join(os.path.dirname(__file__), "results", f"nondimension_lv2_pred_df_{timestamp}.csv"))

    save_to_db(df, pred_version, scenarios='LV2')

