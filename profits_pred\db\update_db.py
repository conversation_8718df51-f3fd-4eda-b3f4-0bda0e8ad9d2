from sqlalchemy import MetaData, Table, delete
import logging

import warnings
import pandas as pd

from profits_pred.src.utils.resource_loader import RDB_POOLS

warnings.filterwarnings("ignore")


def delete_date_by_expert_data_type(
        table_name,
        time,
        expert_data_type
):
    """
        根据专家预测数据类型删除会计期中的相应数据

        Parameters
        ----------
        args
            数据库连接参数
        table_name
            数据库表名
        time
            会计期
        expert_data_type
            专家预测的数据类型: 量纲分析师，产业分析师

        Returns
        -------

    """
    session = next(RDB_POOLS["pg_demo"].get_session_with_commit())
    try:
        # 获取表结构
        engine = RDB_POOLS["pg_demo"]._engine
        metadata = MetaData()
        table = Table(table_name, metadata, autoload_with=engine)
        # 构建更新条件
        update_condition = (
                (table.c.period_id == time) &
                (table.c.combined_expert == expert_data_type)
        )
        logging.info(f"正在删除表:{table_name}," f"会计期:{time},专家类型{expert_data_type}")
        session.execute(
            table.update()
            .where(update_condition)
            .values(del_flag='Y')
        )
        session.commit()
        logging.info('已更新数据库删除标志')
    except Exception as e:
        session.rollback()
        logging.error(
            f"更新删除标志失败 - 表: {table_name}, "
            f"会计期: {time}, 专家类型: {expert_data_type}, 错误: {str(e)}",
            exc_info=True
        )
        raise
    finally:
        session.close()


def delete_date(
        table_name: str,
        date_time: str
) -> None:
    """
    使用 SQLAlchemy session 删除历史期次数据

    Args:
        table_name: 目标表名
        date_time: 会计期ID

    Raises:
        Exception: 数据库操作失败时抛出
    """
    session = next(RDB_POOLS["pg_demo"].get_session_with_commit())
    try:
        # 获取表对象
        engine = RDB_POOLS["pg_demo"]._engine
        metadata = MetaData()
        table = Table(table_name, metadata, autoload_with=engine)

        # 删除所有类型数据
        condition = (
            (table.c.period_id == date_time)
        )
        logging.info(f"删除所有历史期次数据 - 表: {table_name}, 会计期: {date_time}")
        session.execute(delete(table).where(condition))

        session.commit()
        logging.info("数据删除成功")

    except Exception as e:
        session.rollback()
        logging.error(
            f"删除历史数据失败 - 表: {table_name}, "
            f"会计期: {date_time}, 类型: 'YTD法', 错误: {str(e)}",
            exc_info=True
        )
        raise


def update_table_with_replace(
        df: pd.DataFrame,
        table_name: str
) -> None:
    """
    先删除历史数据，再写入新数据（原子操作）
    :param df: 待写入的 DataFrame
    :param table_name: 目标表名
    """
    # 获取数据库连接和会话
    session = next(RDB_POOLS["pg_demo"].get_session_with_commit())
    engine = RDB_POOLS["pg_demo"]._engine

    # 检查空数据
    if df.empty:
        logging.warning(f"输入 DataFrame 为空，跳过表 {table_name} 的更新")
        return
    # 获取目标表
    metadata = MetaData()
    target_table_obj = Table(table_name, metadata, autoload_with=engine)

    # 插入数据
    data_to_insert = df.to_dict(orient='records')  # 转换为字典格式
    try:
        session.execute(
            target_table_obj.insert(),
            data_to_insert
        )
        session.commit()
        logging.info(f"数据成功插入到表: {table_name}, 行数={len(df)}")
    except Exception as e:
        logging.error(f"数据插入失败: {str(e)}")
        session.rollback()  # 回滚事务
    finally:
        session.close()  # 确保会话被关闭
