import os

from dimension_constant import dimension_subcategory_dim, check_input
from db.get_data import get_data
from db.save_to_db import save_to_db
from profits_sequence_pred import integrate_results
from datetime import datetime
from process_after_pred import after_process_ratio

# 预测因子
nondimension_pred_lst = ['rev_percent', 'mgp_ratio']
# 区间预测
dim_need_interval_prediction = ['unit_cost', 'unit_price']
nondim_need_interval_prediction = ['mgp_ratio']
cols_to_check = dim_need_interval_prediction+nondim_need_interval_prediction


def profit_forecast(pred_version: int):
    with open(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'sql', 'nondimension_sub.sql'), 'r+',
                  encoding='utf-8') as f:
        nondimension_sub_sql = f.read()
        nondimension_sub_sql = nondimension_sub_sql.format(pred_version=pred_version)
    df = get_data(nondimension_sub_sql)
    # 检查结果
    check_input(df, cols_to_check)

    # 无量纲子类
    nondimension_subcategory_his_df = df[
        (df['scenarios'] == '量纲子类') & (df['dimension_subcategory_code'] == 'NOSUB')]

    nondimension_subcategory_pred_df = integrate_results(his_df=nondimension_subcategory_his_df,
                                                         dim=dimension_subcategory_dim, pred_cols=nondimension_pred_lst,
                                                         period_col='target_period', pred_version=pred_version)
    df = nondimension_subcategory_pred_df.groupby(dimension_subcategory_dim + ['target_period']).agg(
        {i + '_fcst': 'max' for i in nondimension_pred_lst} |
        {i + '_fcst_upper': 'max' for i in nondim_need_interval_prediction} |
        {i + '_fcst_lower': 'max' for i in nondim_need_interval_prediction} |
        {i + '_fcst_conf': 'max' for i in nondim_need_interval_prediction}
    ).reset_index()
    df['period_id'] = pred_version
    after_process_ratio(df, nondimension_subcategory_his_df, dimension_subcategory_dim, his_param='mgp_ratio_ytd_data',
                        pre_param='mgp_ratio_fcst')
    # 存在未合并的数据，二次合并
    if df.isnull().any().any():
        df = df.groupby(dimension_subcategory_dim + ['target_period']).agg(
            {i + '_fcst': 'max' for i in nondimension_pred_lst} |
            {i + '_fcst_upper': 'max' for i in nondim_need_interval_prediction} |
            {i + '_fcst_lower': 'max' for i in nondim_need_interval_prediction} |
            {i + '_fcst_conf': 'max' for i in nondim_need_interval_prediction}
        ).reset_index()

    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    df.to_csv(os.path.join(os.path.dirname(__file__), "results", f"nondimension_subcategory_pred_df_{timestamp}.csv"))

    save_to_db(df, pred_version, scenarios='DIM')

