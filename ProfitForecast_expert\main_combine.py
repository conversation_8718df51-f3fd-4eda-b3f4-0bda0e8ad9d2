import time as t

from aipaas.logger_factory import logger

from ProfitForecast_expert.profitforecast import func_utils
from ProfitForecast_expert.profitforecast.utils.combine_data import combine_expert_result, get_combine_tbl_names


def combine_expert(
    env: str,
    time: str,
    expert_data_type: str
):
    """
    AI预测融合专家预测结果，并将中间数据写入S3，融合结果写入数据库和S3

    Parameters
    ----------
    env
        运行环境: sit,uat,prod
    time
        会计期
    # task_name
    #     任务名称
    expert_data_type
        专家预测的数据类型: group, chanye, both

    Returns
    -------
    无返回，中间结果写入S3，融合结果写入数据库和S3
    """
    t.strftime('%Y%m%d_%H%M%S', t.localtime())
    ai_fcst_tbl_name, expert_fcst_tbl_names, fact_tbl_name, result_tbl_name = get_combine_tbl_names(expert_data_type)

    # 计算融合预测结果
    db_table_names = (ai_fcst_tbl_name, expert_fcst_tbl_names, fact_tbl_name)
    middle_result, combined_result = combine_expert_result(
        db_table_names, time, env, expert_data_type
    )

    # 去重
    key = ['period_id', 'target_period', 'bg_code', 'bg_name', 'oversea_desc',
       'lv1_code', 'lv1_name', 'lv2_code', 'lv2_name', 'phase_date', 'chanye_level',
       'chanye_name', 'chanye_code', 'target_period_level', 'combined_expert']

    combined_result = combined_result.groupby(key).max().reset_index()


    # 融合结果写入数据库
    wright_args = {
        "table_name": result_tbl_name,
        "time": time,
        "change_acc": False,  # 是否修正acc列的属性
        "expert_data_type": expert_data_type  # 专家数据类型
    }
    replace_blank_fields = ["lv2_code", "lv2_name", "phase_date"]
    combined_result_ = func_utils.pg_write_data(
        combined_result, env, wright_args, delete_label="all", replace_blank_fields=replace_blank_fields
    )  # 先删除相同会计期数据，再写入
    logger.info(f"融合结果写入db完成")
