import argparse
import base64
import json
import os

import requests
from aipaas.env import env_mode
from aipaas.logger_factory import logger

from ProfitForecast_expert.profitforecast.utils.default_conf import COMBINE_EXPERT_TASK


def get_basic_token(appid, token):
    # token通过base64加密(API接口要求，不加密无法正常生产动态Token)
    jm = base64.b64encode(token.encode('utf-8'))
    jmstr = str(jm)
    # API接口url
    if env_mode == "prod":
        url = "http://w3cloud.huawei.com/ApiCommonQuery/appToken/getRestAppDynamicToken"
    else:
        url = "http://kwe-beta.huawei.com/ApiCommonQuery/appToken/getRestAppDynamicToken"
    # API接口header定义
    header = {'Content-Type': 'application/json'}
    # API接口body定义
    body = {
        "appId": appid,
        "credential": jmstr[2:-1]
    }
    # API连接
    response = requests.post(url, headers=header, data=json.dumps(body))
    # Basic_Token信息提取
    basic_token = json.loads(response.text)['result']
    return basic_token


def lts_back(lts_job_id, status):
    logger.info(f"LTS回调接口开始-status:{status} ---")
    # LTS回调接口url、生成人、APPID、token定义
    if env_mode == "prod":
        lts_url = "http://lts.fin.his.huawei.com/lts/service/v1/job/commitAsyncJobStatus"
        static_token = os.environ.get("static_token")
    else:
        lts_url = "http://lts.biz.his-beta.huawei.com/lts/service/v1/job/commitAsyncJobStatus"
        static_token = os.environ.get("static_token")
    context = {
        'url_service': lts_url,
        'createname': 'zhouboxiao',
        'APP_ID': 'com.huawei.finance.ai.opt.fop',
        'token': static_token
    }
    # 生成动态Token
    authorization = get_basic_token(context.get('APP_ID'), context.get('token'))
    # 回调接口header定义
    header = {'Authorization': str(authorization)}
    # 回调接口body定义
    data = {
        "asyncjobinfo": "{'LTS_JOB_ID':'%s','LTS_JOB_STATUS':'%s','V': '1.0'}" % (lts_job_id, status),
        "w3c": context.get('createname'),
        "source": "lts",
        "appid": context.get('APP_ID')
    }
    # 接口url获取
    url = context.get('url_service')
    # 接口运行
    response = requests.post(url, headers=header, data=data)
    logger.info(f"LTS回调接口结束-status:{status}---")
    return response.text


def parse_args():
    """
    解析LTS输入参数
    :return:
    """
    parser = argparse.ArgumentParser(description="aipaas demo args")
    parser.add_argument("--EXEC_DATE", help="help of EXEC_DATE")
    parser.add_argument("--LTS_JOB_ID", help="help of LTS_JOB_ID")
    parser.add_argument("--TARGET_LEVEL", help="help of TARGET_LEVEL")
    parser.add_argument("--METHOD", help="help of METHOD")
    args = parser.parse_args()
    # 日志打印
    logger.info("EXEC_DATE:{}".format(args.EXEC_DATE) + "---")
    logger.info("LTS_JOB_ID:{}".format(args.LTS_JOB_ID) + "---")
    logger.info("TARGET_LEVEL:{}".format(args.TARGET_LEVEL) + "---")
    logger.info("METHOD:{}".format(args.METHOD) + "---")
    return args


def parse_args_for_combine():
    """
    解析AI预测融合专家预测结果任务LTS输入参数
    """
    parser = argparse.ArgumentParser(description="aipaas demo args")
    parser.add_argument("--EXEC_DATE", help="help of EXEC_DATE")
    parser.add_argument("--LTS_JOB_ID", help="help of LTS_JOB_ID")
    parser.add_argument("--EXPERT_TYPE", help="group,chanye,both")
    args = parser.parse_args()
    # 日志打印
    logger.info("EXEC_DATE:{}".format(args.EXEC_DATE) + "---")
    logger.info("LTS_JOB_ID:{}".format(args.LTS_JOB_ID) + "---")
    logger.info("EXPERT_TYPE:{}".format(args.EXPERT_TYPE) + "---")

    return args


def parse_args_for_validate():
    """
    集团盈利预测数据校验任务参数解析 - 集团盈利各个层级和融合专家预测在一个任务里做数据校验
    """
    parser = argparse.ArgumentParser(description="task args")
    parser.add_argument("--TASK_NAME", type=str, required=True, choices=[AI_FORECAST_TASK, COMBINE_EXPERT_TASK],
                        help=f"数据校验任务名称:{AI_FORECAST_TASK}, {COMBINE_EXPERT_TASK}")
    parser.add_argument("--EXEC_DATE", type=str, required=True, help="会计期，如:'202410'")
    parser.add_argument("--LTS_JOB_ID", type=str, required=True, help="LTS任务的job_id")
    args = parser.parse_args()

    # 日志打印
    logger.info("集团盈利预测数据校验任务参数:")
    logger.info("TASK_NAME:{}".format(args.TASK_NAME) + "---")
    logger.info("EXEC_DATE:{}".format(args.EXEC_DATE) + "---")
    logger.info("LTS_JOB_ID:{}".format(args.LTS_JOB_ID) + "---")

    return args
