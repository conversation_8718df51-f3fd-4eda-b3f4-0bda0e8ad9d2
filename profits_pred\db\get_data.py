import warnings

import pandas as pd
from profits_pred.pyxis.utils.config_loader import ConfigLoader
from sqlalchemy import text

from profits_pred.src.utils.resource_loader import RDB_POOLS

warnings.filterwarnings("ignore")

def get_data(sql:str):

    session = next(RDB_POOLS["pg_demo"].get_session_with_commit())
    # 取数
    session.rollback()  # sql报错之后需要先回滚才能运行
    results = session.execute(text(sql))
    columns = results.cursor.description
    rows = results.fetchall()
    results_with_columns = [dict(zip([col[0] for col in columns], row)) for row in rows]
    df = pd.DataFrame(results_with_columns)
    return df