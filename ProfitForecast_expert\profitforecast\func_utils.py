import copy
from typing import List, Dict

import numpy as np
import pandas as pd
from aipaas.logger_factory import logger

from ProfitForecast_expert.profitforecast.utils.default_conf import (
    NULL_STRING_PLACE_HOLDER,
    CHANYE_EXPERT_TYPE,
    CHANYE_EXPERT_MARK,
    DIM_EXPERT_TYPE,
    DIM_EXPERT_MARK, L1_FOP_TBL_NAME, L1_RES_TBL_NAME
)
from ProfitForecast_expert.profitforecast.utils.func import get_other_columns
from profits_pred.db.update_db import delete_date_by_expert_data_type, delete_date, update_table_with_replace
from profits_pred.db.get_data import get_data


class ReplaceMcaAdjustRatio:
    """替换海外Mca调整率预测异常的问题

    """

    def __init__(
            self,
            run_env,
            time
    ):
        self.run_env = run_env
        self.time = str(time)

    def _get_connect(self, sql):
        args = get_pg_params(self.run_env)
        db_conn = get_db_corn(**args)
        fop_data = pd.read_sql_query(sql, db_conn)
        db_conn.close()
        return fop_data

    def _get_history_data(self):
        args = get_pg_params(self.run_env)
        table_name = L1_FOP_TBL_NAME
        data = get_kr_fcst_data(self.time, table_name, **args)
        _, history_data, _ = get_l1_ytd_data(data, self.time, target_level="海外")
        return history_data

    def _get_global_data(self, method):
        table_name = L1_RES_TBL_NAME
        if "YTD" in method:
            sql = f"SELECT * FROM {table_name} WHERE period_id='{self.time}' and oversea_desc='全球' " \
                  f"and length(fcst_type) > 0 and fcst_type <> '{NULL_STRING_PLACE_HOLDER}'"
        else:
            sql = f"SELECT * FROM {table_name} WHERE period_id='{self.time}' and oversea_desc='全球' " \
                  f"and fcst_type in ('', '{NULL_STRING_PLACE_HOLDER}')"
        data = self._get_connect(sql)
        return data

    def _get_oversea_data(self, method):
        table_name = L1_RES_TBL_NAME
        if "YTD" in method:
            sql = f"SELECT * FROM {table_name} where period_id='{self.time}' and oversea_desc='海外' " \
                  f"and length(fcst_type) > 0 and fcst_type <> '{NULL_STRING_PLACE_HOLDER}'"
        else:
            sql = f"SELECT * FROM {table_name} where period_id='{self.time}' and oversea_desc='海外' " \
                  f"and fcst_type in ('', '{NULL_STRING_PLACE_HOLDER}')"
        data = self._get_connect(sql)
        data.to_csv("海外_l1_new.csv", index=False)
        return data

    @staticmethod
    def make_unite(df):
        unite_cols = ["bg_name", "lv1_name", "lv2_name", "l1_name"]
        df["unite"] = df[unite_cols].apply("_".join, axis=1)

        return df

    def _get_time(self):
        time = str(float(self.time) - 100)[:4] + "-12"
        return time

    def _replace_data(self, df1, df2):
        filtered_df2 = df2[["unite", "target_period", "mca_adjust_ratio_fcst"]]
        merged_df = pd.merge(df1, filtered_df2, on=['unite', 'target_period'], how='left', suffixes=('_old', '_new'))
        merged_df['mca_adjust_ratio_fcst_old'] = merged_df['mca_adjust_ratio_fcst_new'].fillna(
            merged_df['mca_adjust_ratio_fcst_old']
        )
        merged_df.drop(columns=['mca_adjust_ratio_fcst_new'], inplace=True)
        merged_df = merged_df.rename(columns={"mca_adjust_ratio_fcst_old": "mca_adjust_ratio_fcst"})
        return merged_df

    def replace_data(self, method):
        logger.info(f"method:{method}")
        oversea_data = self._get_oversea_data(method)
        history_data = self._get_history_data()
        global_data = self._get_global_data(method)
        oversea_data_copy = oversea_data[
            (oversea_data["mca_adjust_ratio_fcst"] > 1) ^ (oversea_data["mca_adjust_ratio_fcst"] < -1)]
        oversea_df = oversea_data_copy[oversea_data_copy["fcst_type"].notna()]
        logger.info(f"oversea_df shape:{oversea_df.shape}")
        if not oversea_df.shape[0]:
            return oversea_data
        oversea_df = self.make_unite(oversea_df)
        history_data = self.make_unite(history_data)
        global_data = self.make_unite(global_data)
        oversea_data = self.make_unite(oversea_data)
        unit_list = oversea_df["unite"].unique().tolist()

        time = self._get_time()
        for i in unit_list:
            target_history_data = float(
                history_data[(history_data["unite"] == i) & (history_data["period_id"] == time)]["mca_adjust_rate"]
            )
            if target_history_data > 1 or target_history_data < -1:
                target_history_data_ytd = global_data[
                    (global_data["unite"] == i) & (global_data["fcst_type"].notna())]
                oversea_data = self._replace_data(oversea_data, target_history_data_ytd)
                continue
            oversea_data.loc[
                (oversea_data["unite"] == i) & (oversea_data["fcst_type"].notna()),
                "mca_adjust_ratio_fcst"
            ] = target_history_data

        logger.info(f"after replace, oversea_df shape:{oversea_df.shape}")

        return oversea_data

    def save_db(self):
        ytd_data = self.replace_data("YTD")
        table_name = L1_RES_TBL_NAME
        wright_args = {
            "table_name": table_name,
            "time": self.time,
            "target_level": "海外"
        }
        ytd_data["fcst_type"] = ytd_data["fcst_type"].fillna(NULL_STRING_PLACE_HOLDER)
        data_concat = pg_write_data(ytd_data, self.run_env, wright_args, delete_label="时序法")
        month_data = self.replace_data("分月")
        wright_args = {
            "table_name": table_name,
            "time": self.time,
            "target_level": "海外"
        }
        month_data["fcst_type"] = NULL_STRING_PLACE_HOLDER
        data_concat = pg_write_data(month_data, self.run_env, wright_args, delete_label=NULL_STRING_PLACE_HOLDER)


class ReplaceData:
    """结果后处理逻辑，异常均本均价结果替换

    """

    def __init__(
            self, data,
            train_test_df,
            time,
            tag="month"
    ):
        self.data = copy.deepcopy(data)
        self.train_test_df = train_test_df
        self.time = time
        self.tag = tag

    def _judge_data(self):
        """筛选数据中的预测的异常值

        Returns
        -------
        unite名称 list
        """
        self.data["diff"] = self.data["unit_cost_fcst"] - self.data["unit_price_fcst"]
        anomaly_list = self.data[self.data["diff"] > 0]["unite"].unique().tolist()
        return anomaly_list

    def _judge_percent(self):
        """筛选设备收入占比的预测的异常值

        Returns
        -------

        """
        anomaly_list = self.data[self.data["rev_percent_fcst"] > 1]["unite"].unique().tolist()
        return anomaly_list

    def _replace_anomaly_data(self, data):
        """替换数据中本大于价的情况

        Parameters
        ----------
        data 待处理数据

        Returns
        -------
        处理后的数据
        """
        unite_name = str(data["unite"].iloc[0])
        unit_cost_name = unite_name + "_unit_cost"
        unit_price_name = unite_name + "_unit_price"
        history_price = self.train_test_df[self.train_test_df["UNION_ATTR"] == unit_price_name]
        history_cost = self.train_test_df[self.train_test_df["UNION_ATTR"] == unit_cost_name]
        data = self._replace_method(history_price, data, "unit_price_fcst")
        data = self._replace_method(history_cost, data, "unit_cost_fcst")
        return data

    def _replace_anomaly_percent(self, data):
        unite_name = str(data["unite"].iloc[0])
        rev_percent_name = unite_name + "_rev_percent"
        history_percent = self.train_test_df[self.train_test_df["UNION_ATTR"] == rev_percent_name]
        data = self._replace_method(history_percent, data, "rev_percent_fcst")
        return data

    def _replace_method(self, history_data, anomaly_data, name):
        """替换逻辑

        Parameters
        ----------
        history_data: 历史数据,已经具体到某条时序
        anomaly_data: 异常结果
        name: 替换名称

        Returns
        -------

        """
        target_year = str(int(self.time[:4]) - 1)
        length = 13 - int(self.time[-2:])
        time_begin = target_year + "-" + self.time[-2:]
        start_time = target_year + "-" + "01"
        end_time = target_year + "-" + "12"
        df = history_data[(history_data["Time"] <= end_time) & (history_data["Time"] >= time_begin)].reset_index(
            drop=True
        )
        if df.shape[0] != length:
            return anomaly_data
        if self.tag == "ytd":
            df_2024 = history_data[(history_data["Time"] <= end_time) & (history_data["Time"] >= start_time)]
            if df_2024.shape[0] != 12:
                return anomaly_data
            df = pd.concat([df, df_2024]).reset_index(drop=True)
        anomaly_data = anomaly_data.reset_index(drop=True)
        anomaly_data[name] = df["Data"]
        return anomaly_data

    def transform(self):
        """替换功能主入口

        Returns
        -------

        """
        anomaly_list = self._judge_data()
        anomaly_percent_list = []
        if "rev_percent_fcst" in self.data.columns.tolist():
            anomaly_percent_list = self._judge_percent()
        replaced_data = pd.DataFrame()
        replaced_percent = pd.DataFrame()
        for i in anomaly_list:
            df = self.data[self.data["unite"] == i].reset_index(drop=True)
            df = self._replace_anomaly_data(df)
            replaced_data = pd.concat([replaced_data, df])
        for i in anomaly_percent_list:
            df = self.data[self.data["unite"] == i].reset_index(drop=True)
            df = self._replace_anomaly_percent(df)
            replaced_percent = pd.concat([replaced_percent, df])

        data = pd.DataFrame()
        for i in self.data["unite"].unique().tolist():
            if i in anomaly_list + anomaly_percent_list:
                continue
            df = self.data[self.data["unite"] == i]
            data = pd.concat([data, df])
        res = pd.concat([data, replaced_data, replaced_percent])
        return res

    def replace_percent_method(self, res):
        """替换rev_percent

        Parameters
        ----------
        res

        Returns
        -------

        """
        anomaly_percent_list = self._judge_percent()
        data = pd.DataFrame()
        replaced_percent = pd.DataFrame()
        for i in anomaly_percent_list:
            df = res[res["unite"] == i].reset_index(drop=True)
            df = self._replace_anomaly_percent(df)
            replaced_percent = pd.concat([replaced_percent, df])
        for i in self.data["unite"].unique().tolist():
            if i in anomaly_percent_list:
                continue
            df = self.data[self.data["unite"] == i]
            data = pd.concat([data, df])
        new_res = pd.concat([data, replaced_percent])
        return new_res


def change_time(x):
    """修改时间列的格式函数

    Parameters
    ----------
    x： float 如202212

    Returns
    -------
    str 2022-12
    """
    a = str(x).split(".")[0]
    b = a[:4] + "-" + a[-2:]
    return b


def change(x):
    """修改存储到S3的分布数据的type列

    Parameters
    ----------
    x: str

    Returns
    -------
    str
    """
    if x == "mgp_rate":
        return "mgp_rate_after"
    elif x == "equip_rev_cons_after_amt":
        return "equip_rev_after"
    return x


def change_acc_method(data_concat):
    """修正acc列的属性

    Parameters
    ----------
    data_concat: 预测结果

    Returns
    -------
    dataframe
    """
    columns = ["unit_price_fcst_acc", "unit_cost_fcst_acc", "mgp_ratio_fcst_acc", "equip_rev_cons_after_amt_fcst_acc",
               "rev_percent_fcst_acc", "carryover_ratio_fcst_acc", "mca_adjust_ratio_acc", "mgp_adjust_ratio_acc"]
    for i in columns:
        if i in data_concat.columns:
            data_concat[i] = data_concat[i].apply(
                lambda x: np.nan if x == " " else x
            )
            data_concat[i] = data_concat[i].apply(lambda x: np.nan if x < -1e4 else x)
    return data_concat


def pg_write_data(data_concat: pd.DataFrame,
                  run_env: str,
                  wright_args: Dict,
                  delete_label: str = "all",
                  replace_blank_fields: List[str] = None
                  ):
    """预测结果写回数据库，需要先删除相同会计期数据，再写回

    Parameters
    ----------
    data_concat
        待写入数据
    run_env
        环境变量
    wright_args
        kv对参数
    delete_label
        删除表数据的策略标记
    replace_blank_fields
        写入数据表后，要将空格替换为空字符串的字段列表

    Returns
    -------
    存到数据库的数据
    """
    time = wright_args.get("time")
    table_name = wright_args.get("table_name")
    sql = f'SELECT * FROM {table_name} LIMIT 1'

    # 删除相同会计期的数据
    if wright_args.get("expert_data_type"):
        expert_data_type = wright_args.get("expert_data_type")
        expert_marks = {
            DIM_EXPERT_TYPE: DIM_EXPERT_MARK,
            CHANYE_EXPERT_TYPE: CHANYE_EXPERT_MARK
        }
        if expert_data_type != 'both' and expert_data_type not in expert_marks:
            raise ValueError(f"无效的专家数据类型: {expert_data_type}")
        if expert_data_type != 'both':
            delete_date_by_expert_data_type(table_name, time, expert_marks[expert_data_type])
        else:
            for k, v in expert_marks.items():
                delete_date_by_expert_data_type(table_name, time, v)
    else:
        delete_date(table_name, time)

    # 筛选字段
    data_result = get_data(sql)  # 获取要写入表的列名列表
    data_concat = get_other_columns(data_concat)  # 获取其他需要写入表的列名列表
    data_concat1 = data_concat[data_result.columns.tolist()]
    # 删除重复行
    data_concat1 = data_concat1.drop_duplicates()
    # 数据写入
    logger.info(f"待写入db的数据大小:{data_concat1.shape}")
    update_table_with_replace(data_concat1, table_name)
    logger.info("数据写入db操作完成")

    return data_concat1


def replace_empty(args: Dict,
                  table_name: str,
                  time: str,
                  default_field: str = "fcst_type",
                  fields: List[str] = None
                  ):
    """修改存到数据库的结果，将空格替换为空，不替换的话预测推演取数会报错

    Parameters
    ----------
    args
        数据库连接参数
    table_name
        数据库表名
    time
        会计期
    default_field
        默认要替换的字段
    fields
        需要将空格替换为空字符串的字段名列表
    """
    if fields is None:
        fields = []
    if default_field:
        fields.append(default_field)
    fields = list(set(fields))

    sql_tmpl = f"UPDATE {table_name} SET REPLACE_FIELD = '' " \
               f"WHERE period_id = '{time}' AND REPLACE_FIELD = '{NULL_STRING_PLACE_HOLDER}'"

    db_conn, cur = None, None
    try:
        db_conn = get_db_corn(**args)
        cur = db_conn.cursor()
        for field in fields:
            sql_empty = sql_tmpl.replace("REPLACE_FIELD", field)
            cur.execute(sql_empty)
        db_conn.commit()
    finally:
        if cur is not None:
            cur.close()
        if db_conn is not None:
            db_conn.close()


def lv1_l1_datapreprocess(data, time, target_level="全球"):
    """lv1和l1的数据预处理

    Parameters
    ----------
    data: 数据库读取的原始数据
    time: YTD数据
    target_level: 目标层级

    Returns
    -------
    dataframe(预处理数据)、list(几年)
    """
    train_data = data[(data["currency"] == "CNY") & (data["bg_name"] != "其它")].reset_index(drop=True)
    train_data = train_data[
        (train_data["bg_code"] == "PROD0002") ^ (train_data["bg_code"] == "PDCG901160") ^
        (train_data["bg_code"] == "PDCG901159")]
    train_data = train_data[train_data["oversea_desc"] == target_level]
    train_data["period_id"] = train_data["period_id"].apply(lambda x: change_time(x))
    train_data["period_id"] = train_data["period_id"].astype(str)
    df = train_data
    year_list = [_ for _ in range(int(str(data["period_id"].min())[:4]), int(str(time)[:4]) + 1)]
    return df, year_list


def save_last_month(data, time):
    """YTD数据取出半年度、季度数据

    Parameters
    ----------
    data: 预测结果
    time: 会计期

    Returns
    -------
    dataframe
    """
    max_date = data["target_period"].max()
    max_date_copy = str(int(float(max_date) - 100))
    df = data[(data["target_period"] == max_date) ^ (data["target_period"] == max_date_copy)]
    df["target_period"] = df["target_period"].apply(lambda x: x[:4])
    logger.info(f"时间为：{time}")

    # 半年度
    if int(time[-2:]) <= 6:
        mid_df = data[data["target_period"] == str(int(time[:-2]) * 100 + 6)]
        mid_df["target_period"] = time[:-2] + "H1"
        logger.info(f"时间为：{time[:-2] + 'H1'}")
        df = pd.concat([df, mid_df])

    # 季度
    if 1 <= int(time[-2:]) <= 3:
        q1_df = data[data["target_period"] == str(int(time[:-2]) * 100 + 3)]
        q1_df["target_period"] = time[:-2] + "Q1"
        logger.info(f"时间为：{time[:-2] + 'Q1'}")
        df = pd.concat([df, q1_df])
    elif 7 <= int(time[-2:]) <= 9:
        q3_df = data[data["target_period"] == str(int(time[:-2]) * 100 + 9)]
        q3_df["target_period"] = time[:-2] + "Q3"
        logger.info(f"时间为：{time[:-2] + 'Q3'}")
        df = pd.concat([df, q3_df])
    df["fcst_type"] = "时序法"
    return df


def save_distribution_month(data, time):
    """处理分布数据中的和半年度、季度数据

    Parameters
    ----------
    data: 预测结果
    time: 会计期

    Returns
    -------
    dataframe
    """
    digit_columns = []
    for i in data.columns:
        if "20" in i:
            digit_columns.append(i)
    data = data.rename(
        columns={
            max(digit_columns): max(digit_columns)[:4],
            str(int(float(max(digit_columns)) - 100)): str(int(float(max(digit_columns)) - 100))[
                                                       :4]
        }
    )
    if int(time[-2:]) <= 6:
        data = data.rename(columns={str(int(time[:-2]) * 100 + 6): (str(int(time[:-2])) + 'H1')})
    if 1 <= int(time[-2:]) <= 3:
        data = data.rename(columns={str(int(time[:-2]) * 100 + 3): (str(int(time[:-2])) + 'Q1')})
    elif 7 <= int(time[-2:]) <= 9:
        data = data.rename(columns={str(int(time[:-2]) * 100 + 9): (str(int(time[:-2])) + 'Q3')})

    for i in digit_columns:
        if i in data.columns:
            data = data.rename(columns={i: i + "-"})
    return data


def l2_data_preprocess(data, target_level):
    """l2数据预处理

    Parameters
    ----------
    data: 从数据库读取的数
    target_level: 目标层级

    Returns
    -------
    预处理数据
    """
    train_data = data[(data["currency"] == "CNY")].reset_index(drop=True)
    unite_cols = ["bg_name", "lv1_name", "lv2_name", "l1_name", "l2_name"]
    train_data["unite"] = train_data[unite_cols].apply("_".join, axis=1)

    train_data = train_data[
        (train_data["bg_code"] == "PROD0002") ^ (train_data["bg_code"] == "PDCG901160") ^
        (train_data["bg_code"] == "PDCG901159")]

    train_data = train_data[train_data["oversea_desc"] == target_level]
    train_data = train_data.drop(train_data[train_data['articulation_flag'] == 3].index)
    train_data = train_data.dropna(axis=0, subset=['l2_name'])
    train_data["phase_date"] = train_data["phase_date"].apply(
        lambda x: np.nan if x == NULL_STRING_PLACE_HOLDER else x
    )
    train_data = train_data.drop(
        train_data[(train_data['phase_date'].notna()) & (train_data['phase_date'] != NULL_STRING_PLACE_HOLDER)].index
    )
    train_data["period_id"] = train_data["period_id"].apply(lambda x: change_time(x))
    train_data["period_id"] = train_data["period_id"].astype(str)
    return train_data


def replace_mgp_rate(data_res, data_orl, time):
    """对预测异常的制毛率进行条件修正，对在(-1, 1)之外的数据用去年全年替换

    Parameters
    ----------
    data_res: 预测结果
    data_orl: 原始数据
    time: 会计期

    Returns
    -------
    dataframe
    """
    result = pd.DataFrame()
    last_time = str(int(str(time)[:4]) - 1) + "-12"
    for i in data_res["unite"].unique().tolist():
        df = data_res[data_res["unite"] == i]
        try:
            last_mgp = float(
                data_orl[(data_orl["period_id"] == last_time) & (data_orl["unite"] == i)]["mgp_rate"].iloc[0]
            )
        except Exception as e:
            last_mgp = 0.
        if not -1 <= last_mgp <= 1:
            last_mgp = 0.
        mgp_copy = last_mgp
        df["mgp_rate_fcst"] = df["mgp_rate_fcst"].apply(lambda x: mgp_copy if (x > 1 or x < -1) else x)
        result = pd.concat([result, df])
    return result


def replace_price_and_cost(data_res, data_orl, time):
    """对均本均价小于0的用去年全年代替

    Parameters
    ----------
    data_res: 预测结果
    data_orl: 原始数据
    time: 会计期

    Returns
    -------
    dataframe
    """
    result = pd.DataFrame()
    last_time = str(int(str(time)[:4]) - 1) + "-12"
    for i in data_res["unite"].unique().tolist():
        df = data_res[data_res["unite"] == i]
        last_cost = get_last_cost(data_orl, last_time, i)
        last_price = get_last_price(data_orl, last_time, i)
        cost_copy = last_cost
        price_copy = last_price
        df["unit_cost_fcst"] = df["unit_cost_fcst"].apply(lambda x: cost_copy if x < 0 else x)
        df["unit_price_fcst"] = df["unit_price_fcst"].apply(lambda x: price_copy if x < 0 else x)
        result = pd.concat([result, df])
    return result


def get_last_cost(data_orl, last_time, i):
    """均本处理逻辑

    Parameters
    ----------
    data_orl: 原始数据
    last_time: 替换月份
    i: unite

    Returns
    -------
    float
    """
    try:
        last_cost = float(
            data_orl[(data_orl["period_id"] == last_time) & (data_orl["unite"] == i)]["unit_cost"].iloc[0]
        )
    except Exception as e:
        last_cost = 0.
    if last_cost == float("inf") or last_cost <= 0 or str(last_cost) == "nan":
        last_cost = 0.
    return last_cost


def get_last_price(data_orl, last_time, i):
    """均价处理逻辑

    Parameters
    ----------
    data_orl: 原始数据
    last_time: 替换月份
    i: unite

    Returns
    -------
    float
    """
    try:
        last_price = float(
            data_orl[(data_orl["period_id"] == last_time) & (data_orl["unite"] == i)]["unit_price"].iloc[0]
        )
    except Exception as e:
        last_price = 0.
    if last_price == float("inf") or last_price <= 0 or str(last_price) == "nan":
        last_price = 0.
    return last_price


def get_data_diff(data_res_ytd, data_res_ytd1, method_list):
    data_res_ytd["target_period"] = data_res_ytd["target_period"].apply(lambda x: str(x))
    data_res_ytd1["target_period"] = data_res_ytd1["target_period"].apply(lambda x: str(x))
    data_res_ytd1 = data_res_ytd1.sort_values(by=["unite", "target_period"]).reset_index(drop=True)
    data_res_ytd = data_res_ytd.sort_values(by=["unite", "target_period"]).reset_index(drop=True)
    for method in method_list:
        data_res_ytd1[method] = data_res_ytd1[method + "_fcst"] - data_res_ytd[method + "_fcst"]
        data_res_ytd1[method + "_fcst_lower"] = data_res_ytd1[method + "_fcst_lower"] + data_res_ytd1[method]
        data_res_ytd1[method + "_fcst_upper"] = data_res_ytd1[method + "_fcst_upper"] + data_res_ytd1[method]
    return data_res_ytd1


def make_unite(train_data):
    if "l2_name" in train_data.columns.tolist():
        unite_cols = ["bg_name", "lv1_name", "lv2_name", "l1_name", "l2_name"]
        train_data["unite"] = train_data[unite_cols].apply("_".join, axis=1)
    elif "l1_name" in train_data.columns.tolist():
        unite_cols = ["bg_name", "lv1_name", "lv2_name", "l1_name"]
        train_data["unite"] = train_data[unite_cols].apply("_".join, axis=1)
    else:
        unite_cols = ["bg_name", "lv1_name"]
        train_data["unite"] = train_data[unite_cols].apply("_".join, axis=1)

    return train_data


def adjust_distribution_data(distribution, data, method_list):
    distribution = make_unite(distribution)
    unite_plus_cols = ["oversea_desc", "unite"]
    data["unite_plus"] = data[unite_plus_cols].apply("_".join, axis=1)
    distribution["unite_plus"] = distribution[unite_plus_cols].apply("_".join, axis=1)
    for method in method_list:
        data_diff = data[(data[method] != 0) & (data[method].notna())]
        diff_list = [data_diff[method].tolist(), data_diff["unite_plus"].tolist(), data_diff["target_period"].tolist()]
        if len(diff_list[0]):
            for i in range(len(diff_list[0])):
                mask = (distribution["unite_plus"] == diff_list[1][i]) & (distribution["type"] == method)
                distribution.loc[mask, diff_list[2][i]] += diff_list[0][i]
    return distribution


def preprocess_ytd(data, time):
    """对YTD预测结果后处理

    Parameters
    ----------
    data: YTD预测结果
    time: 会计期

    Returns
    -------
    dataframe
    """
    max_time = str(time[:4]) + "12"
    df = copy.deepcopy(data)
    df = df[df["target_period"] <= max_time]
    df["fcst_type"] = "YTD法"
    return df


def preprocess_distribution(data, col_list):
    """保留分布数据10位小数

    Parameters
    ----------
    data: 分布数据
    col_list: 待约束列名

    Returns
    -------
    dataframe
    """
    for i in col_list:
        data[i] = data[i].apply(lambda x: round(x, 10))
    return data





def get_last_year_period_id(period_id: str) -> str:
    """获取去年同期的会计期
    """
    res = str(int(period_id[:4]) - 1) + period_id[4:]

    return res


def get_last_month_period_id(period_id: str) -> str:
    """获取当前会计期上个月的会计期
    """
    year = int(period_id[:4])
    month = int(period_id[4:])
    if month > 1:
        last_period_id = str(year) + "%02d" % (month - 1)
    else:
        last_period_id = str(year - 1) + "12"

    return last_period_id


def get_last_quarter_3period_id(period_id: str) -> List[str]:
    """获取上一个季度的三个会计期
    """
    month = int(period_id[4:])
    quarter = (month - 1) // 3 + 1
    last_quarter_year = int(period_id[:4]) if quarter > 1 else int(period_id[:4]) - 1
    res = []
    for m in range(3):
        last_quarter = quarter - 1 if quarter > 1 else 4
        last_quarter_month = m + 1 + 3 * (last_quarter - 1)
        res.append(str(last_quarter_year) + "%02d" % last_quarter_month)

    return res


def get_last_year_special_month_period_id(period_id: str,
                                          month_list: List[int]
                                          ) -> List[str]:
    """获取上一年度指定月份的会计期
    """
    last_year = int(period_id[:4]) - 1

    return [str(last_year) + "%02d" % month for month in month_list]


def add_target_period(period_id,
                      target_code):
    year = period_id[:4]
    month = int(period_id[4:])

    if target_code == 'Y':
        return year
    elif target_code == 'Q':
        quarter = (month - 1) // 3 + 1
        return f"{year}Q{quarter}"
    elif target_code == 'H':
        half = 1 if month <= 6 else 2
        return f"{year}H{half}"
    return None


def get_target_period_level(target_period: str) -> str:
    """获取目标预测期次的时间粒度
    """
    target_period_level = NULL_STRING_PLACE_HOLDER
    if len(target_period) == 4 and target_period.isdigit():
        target_period_level = "year"
    elif len(target_period) == 6 and target_period[4].lower() == "h":
        target_period_level = "half-a-year"
    elif len(target_period) == 6 and target_period[4].lower() == "q":
        target_period_level = "quarter"
    elif len(target_period) == 6 and target_period.isdigit():
        target_period_level = "month"

    return target_period_level


def convert_target_code2period(x: pd.Series) -> str:
    """
    根据会计期将专家预测的预测步长编码转换为预测目标期次
    如 period_id 为 202403, 预测长度编码依次为 年度,半年度，季度，转换结果依次为: 2024, 2024H1, 2024Q1

    Parameters
    ----------
    x
        待转换数据

    Returns
    -------
    转换后的预测目标期次
    """
    data = x.to_dict()
    period_id = data["period_id"]  # 会计期
    target_code = data["target_code"]  # 预测步长编码

    if not isinstance(period_id, str) and len(period_id) != 6:
        msg = f"传入会计期period_id参数据有误:{period_id},请检查"
        raise Exception(msg)

    target_period = period_id[:4]
    if target_code == "年度":
        pass
    elif target_code == "半年度":
        target_period += "H" + str((int(period_id[4:]) - 1) // 6 + 1)
    elif target_code == "季度":
        target_period += "Q" + str((int(period_id[4:]) - 1) // 3 + 1)
    elif target_code == "季度":
        target_period = period_id
    else:
        msg = f"无效的预测步长编码target_code:{target_code}"
        raise Exception(msg)

    return target_period
