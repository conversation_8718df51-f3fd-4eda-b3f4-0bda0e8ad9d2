version: '2.0'
env:
  label: BPIT_Build_Default #构建资源标签
params:
  - name: product
    value: cloudbuild2.0
steps:
  PRE_BUILD:
    - checkout
  BUILD:
    - build_execute:
        command: sh build.sh
        accelerate: false
        check:
         buildcheck:
          project_dir: app.py
          exclude_dir: build.sh;start_listener.sh;start_run.sh;start_server.sh;app.py;config.py;run.py
         sourcecheck:
          - project_type: python
            project_dir: .
         dependency:
          - tool_type: python
            project_dir: .
  POST_BUILD:
    - sh:
        command: | #数字签名配置
          signclient "opt_fcst_profits_model-online.tar.gz"
    - artget: #从云龙上传构建cloudArtifact仓库 需要云龙流水线传入serviceId,serviceName,isRelease参数
        artifact_type: cloudartifact  # 仓库类型
        action: push
        file_path: "package,opt_fcst_profits_model-online.tar.gz;cms,opt_fcst_profits_model-online.tar.gz.cms" #上传包的路径是相对路径，相对于workspace
        version_output_path: .
    - compile_report:
        rules:
          - 'warning /((?i).*\sWarning\s.*)/'
          - 'error /((?i).*\sError\s.*)/'
    - version_set