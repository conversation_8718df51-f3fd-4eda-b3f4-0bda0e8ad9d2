#!/bin/sh

echo "Python Server starting........................"
binPath=$(dirname $0)
cd $binPath

mkdir /applog/logs

yum -y install hostname

pip3 install --index-url https://cmc-cd-mirror.rnd.huawei.com/pypi/simple/ --extra-index-url https://cmc.centralrepo.rnd.huawei.com/artifactory/product_pypi/simple --trusted-host cmc-cd-mirror.rnd.huawei.com --trusted-host cmc.centralrepo.rnd.huawei.com -r requirements.txt
echo "pip3 install requirements done"
pip install AlgorithmFrameworkLogSDK-0.0.2-py3-none-any.whl


if [ "$ENV" = "PROD-ADS" ] || [ "$ENV" = "PROD-AIF" ]; then
  # 安装高斯DB专用驱动
  pip3 uninstall psycopg2 -y
  pip3 uninstall psycopg2-binary -y
  wget https://cmc.centralrepo.rnd.huawei.com/artifactory/api/pypi/pypi-oss/psycopg2/2.9.6rc0+h0.csi.gaussdb.kernel.euleros2.10.r1/psycopg2-2.9.6rc0+h0.csi.gaussdb_kernel.euleros2.10.r1-py3-none-linux_x86_64.whl
  unzip psycopg2-2.9.6rc0+h0.csi.gaussdb_kernel.euleros2.10.r1-py3-none-linux_x86_64.whl
  cp psycopg2 $(python3 -c 'import site; print(site.getsitepackages()[0])') -r
  chmod 755 $(python3 -c 'import site; print(site.getsitepackages()[0])')/psycopg2 -R
  export PYTHONPATH=$(python3 -c 'import site; print(site.getsitepackages()[0])'):$PYTHONPATH
  mkdir -p /usr/local/gaussdb
  cp lib  /usr/local/gaussdb/ -r
  chmod 755 -R /usr/local/gaussdb
  export LD_LIBRARY_PATH=/usr/local/gaussdb/lib:$LD_LIBRARY_PATH
  echo 'export LD_LIBRARY_PATH=/usr/local/gaussdb/lib:$LD_LIBRARY_PATH'>> /etc/profile
fi


# 启动Web服务进程
if [ "$ENV" = "PROD-AIF" ]; then
    uvicorn main:app --host 0.0.0.0 --port 80 --workers "$WORKER_NUM" &
else
    uvicorn main:app --host 0.0.0.0 --port 8080 --workers 1 &
fi


while :
do
 sleep 1
done
