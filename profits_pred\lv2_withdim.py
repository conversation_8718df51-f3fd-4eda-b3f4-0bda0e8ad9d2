import os

from db.get_data import get_data
from db.save_to_db import save_to_db
from profits_sequence_pred import integrate_results
from datetime import datetime
from dimension_constant import lv2_dim, check_input


# 预测因子
lv2_withdim_pred_lst = ['mgp_adjust_ratio', 'mca_adjust_ratio']
lv2_nondim_pred_lst = ['mgp_ratio_after', 'mgp_adjust_ratio', 'mca_adjust_ratio', 'equip_rev_cons_after_amt']
# 区间预测
cols_to_check = lv2_withdim_pred_lst+lv2_nondim_pred_lst


def profit_forecast(pred_version: int):
    with open(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'sql', 'lv2_withdim.sql'), 'r+',
                  encoding='utf-8') as f:
        lv2_withdim_sql = f.read()
        lv2_withdim_sql = lv2_withdim_sql.format(pred_version=pred_version)
    df = get_data(lv2_withdim_sql)
    # 检查结果
    check_input(df, cols_to_check)
    # 有量纲LV2
    lv2_withdim_his_df = df[df['scenarios'].isin(['量纲子类', '量纲分组'])]

    lv2_withdim_pred_df = integrate_results(his_df=lv2_withdim_his_df, dim=lv2_dim, pred_cols=lv2_withdim_pred_lst,
                                            period_col='target_period', pred_version=pred_version)
    df = lv2_withdim_pred_df.groupby(lv2_dim + ['target_period']).agg(
        {i + '_fcst': 'max' for i in lv2_withdim_pred_lst}).reset_index()
    df['period_id'] = pred_version

    # 存在未合并的数据，二次合并
    if df.isnull().any().any():
        df = df.groupby(lv2_dim + ['target_period']).agg(
            {i + '_fcst': 'max' for i in lv2_withdim_pred_lst}).reset_index()

    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    df.to_csv(os.path.join(os.path.dirname(__file__), "results", f"lv2_withdim_pred_df_{timestamp}.csv"))

    save_to_db(df, pred_version, scenarios='LV2')
