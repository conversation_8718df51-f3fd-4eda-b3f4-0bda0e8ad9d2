import os
import sys

from ProfitForecast_expert.profitforecast.utils.lts_client import lts_back

target_folder = 'profits_pred/'
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.join(project_root, target_folder))

import asyncio
from enum import Enum
import requests
from fastapi import APIRouter, Request, status
from profits_pred.src.utils.response_generator import response_generator
from profits_pred.src.task.template import PredTask
from profits_pred.src.utils.resource_loader import LOGGER as log

from profits_pred import dimension_group
from profits_pred import dimension_sub
from profits_pred import nondimension_sub
from profits_pred import nondimension_g
from profits_pred import lv2_nodim
from profits_pred import lv2_withdim
from profits_pred.src.utils.resource_loader import CONFIG
from pydantic import BaseModel

from profits_pred.src.utils.util import current_date

from ProfitForecast_expert.main_combine import combine_expert

router = APIRouter(
    prefix=f"{CONFIG.deploy.service_name}/v1/prediction-tasks",
    tags=["profits_service"],
)


class ProfitStatus(str, Enum):
    SUCCESS = "OK"


class TaskCreate(BaseModel):
    title: str
    callback_url: str


tasks = {}


@router.post("/start", status_code=status.HTTP_200_OK)
async def create_task(
        request: Request,
        predTask: PredTask
):
    # Generate a unique task ID
    lts_job_id = request.headers.get("jobId")
    pred_version = predTask.params.get("pred_version")
    expert_data_type = predTask.params.get("expert_data_type")
    env = predTask.params.get("env")
    task_name = predTask.task_name
    if task_name == '时序预测':
        asyncio.create_task(process_task(pred_version, lts_job_id))
    elif task_name == '融合调优':
        pass
        asyncio.create_task(comb_task(env, pred_version, expert_data_type, lts_job_id))

    return response_generator(
        status.HTTP_200_OK, ProfitStatus.SUCCESS, f"任务启动成功:{lts_job_id}", [{"lts_job_id": lts_job_id}])


async def process_task(pred_version: int, lts_job_id: str):
    if not pred_version:
        pred_version = current_date()
    if isinstance(pred_version, str):
        pred_version = int(pred_version)
    log.info(pred_version)

    try:
        await start_profits(pred_version)
        log.info("时序任务处理结束")
        lts_back(lts_job_id, 1)
    except Exception as e:
        log.error(f"融合调优回调通知失败: {str(e)}")
        lts_back(lts_job_id, 0)


def batch_task(method_list, pred_version):
    for pred_method in method_list:
        try:
            pred_method(pred_version=pred_version)
        except Exception as e:
            log.error("方法{}执行失败：{}".format(pred_method, e))


async def start_profits(pred_version: int):
    '''
    调用6个场景盈利预测
    :param pred_version:
    :return:
    '''
    method_list = [
        dimension_group.profit_forecast,
        dimension_sub.profit_forecast,
        nondimension_sub.profit_forecast,
        nondimension_g.profit_forecast,
        lv2_nodim.profit_forecast,
        lv2_withdim.profit_forecast
    ]
    log.info("[START]开始执行预测任务")
    # log.info('软删除历史数据库的数据')
    # if is_save_enabled():
    #     _del_pred_version_data(target_table, pred_version)

    # 使用 asyncio 并发执行
    # loop = asyncio.get_event_loop()
    # tasks = [loop.run_in_executor(None, method, pred_version) for method in method_list]
    # await asyncio.gather(*tasks)

    # 外层单进程执行：api预测，调用接口时用了8个进程执行
    loop = asyncio.get_running_loop()
    await loop.run_in_executor(None, batch_task, method_list, pred_version)

    log.info("[END]预测任务执行完成")


async def comb_task(env: str, pred_version: int, expert_data_type: str, lts_job_id: int):
    if not pred_version:
        pred_version = current_date()
    if not isinstance(pred_version, str):
        pred_version = str(pred_version)
    log.info(pred_version)
    if not expert_data_type:
        expert_data_type = 'both'
    log.info("预测以下专家类型：{}".format(expert_data_type))

    await start_comb(env, pred_version, expert_data_type)

    try:
        await start_profits(pred_version)
        log.info("融合调优处理结束")
        lts_back(lts_job_id, 1)
    except Exception as e:
        log.error(f"融合调优回调通知失败: {str(e)}")
        lts_back(lts_job_id, 0)


async def start_comb(env: str, pred_version: str, expert_data_type: str):
    """
    调用2个专家的融合调优
    :param env: 运行环境: sit,uat,prod
    :param pred_version: 当前会计期 str
    :param expert_data_type: 专家预测的数据类型: group, chanye, both
    :return:
    """
    method_list = [
        combine_expert
    ]
    log.info("[START]开始执行融合调优任务")

    # 使用 asyncio 并发执行
    loop = asyncio.get_event_loop()
    tasks = [loop.run_in_executor(None, method, env, pred_version, expert_data_type) for method in method_list]
    await asyncio.gather(*tasks)

    log.info("[END]融合调优任务执行完成")
