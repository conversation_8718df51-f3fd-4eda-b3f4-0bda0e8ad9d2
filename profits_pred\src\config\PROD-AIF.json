{"DEPLOY": {"cloud": "HIS", "infra": "AIF", "enable_doc": false, "service_name": "", "uri_prefix": "/mmae-pyxis", "config_server": {"url": "https://apig.hieds.net/api/ConfigCenter/services/saasConfigcenterGetConfig", "app_id": "904eef23d0874c1da63b14d6b1c9e946", "du": "fcst_profits_service", "environment": "similar-general", "region": "kwe4hwsimilar", "version": "1.0", "config_parts": ["35353161622e40fda1336687e0e2646c", "d3bd1b7f84684176b9b538fca568b50e"]}, "api_gateway": {"authorization": {"url": "http://w3cloud.huawei.com/ApiCommonQuery/appToken/getRestAppDynamicToken", "iam_url": "https://iam.his-op.huawei.com/iam/auth/token", "iam_account": "pyxis.iam"}, "authentication": {"sgov_token": "http://w3cloud.huawei.com/ApiCommonQuery/appToken/validateToken", "sgov_uri": "http://w3cloud.huawei.com/ApiCommonQuery/appToken/authorizeByURI"}}}, "LOGGER": {"level": "INFO"}, "RDB": {"pg_demo": {"datasource_name": "fin_dm_opt_fop", "tables": ["dm_fop_ict_pl_sum_t"], "echo": false}}, "OBS": {"demo_bucket": {"bucket": "fai-mlops-demo", "pool_size": 5, "max_overflow": 5, "pool_timeout": 2, "pool_recycle": 3600, "pool_pre_ping": true}}, "AIF": {"base_url": "https://console.his-op.huawei.com"}, "GES": {"pyxis_demo": {"account": "替换成生产的j2c account", "iam_endpoint": "替换成生产的iam_endpoint", "domain_name": "替换成生产的domain_name", "project_id": "替换成生产的project_id", "graph_name": "替换成生产的graph_name", "graph_endpoint": "替换成生产的graph_endpoint", "pool_size": 5, "max_overflow": 5, "pool_timeout": 2, "pool_recycle": 3600, "pool_pre_ping": true}}, "LIMITER": {"storage_uri": null, "strategy": "moving-window", "unit": "minute", "frequency": 10}, "APP_CONFIG": {"logging_max_body_length": 1024, "logging_separator": "\n", "logging_custom_headers": ["X-TENANTID", "X-APPID", "X-USERID"]}}