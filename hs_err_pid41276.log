#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 264241152 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3825), pid=41276, tid=25292
#
# JRE version:  (17.0.9+7) (build )
# Java VM: OpenJDK 64-Bit Server VM (17.0.9+7-b1000.46, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.editor.GitRebaseEditorApp E:/code-hub/opt.fcst.profits.model/.git/COMMIT_EDITMSG

Host: 12th Gen Intel(R) Core(TM) i7-1260P, 16 cores, 15G,  Windows 11 , 64 bit Build 22621 (10.0.22621.3527)
Time: Mon Jul 21 17:18:15 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.3527) elapsed time: 0.009090 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000001b3dcce6a30):  JavaThread "Unknown thread" [_thread_in_vm, id=25292, stack(0x0000005a74c00000,0x0000005a74d00000)]

Stack: [0x0000005a74c00000,0x0000005a74d00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6886e9]
V  [jvm.dll+0x841e4a]
V  [jvm.dll+0x843a8e]
V  [jvm.dll+0x8440f3]
V  [jvm.dll+0x24c14f]
V  [jvm.dll+0x685499]
V  [jvm.dll+0x679c2a]
V  [jvm.dll+0x30cf9b]
V  [jvm.dll+0x314446]
V  [jvm.dll+0x36425e]
V  [jvm.dll+0x36448f]
V  [jvm.dll+0x2e2d68]
V  [jvm.dll+0x2e3cd4]
V  [jvm.dll+0x8129f1]
V  [jvm.dll+0x3720c1]
V  [jvm.dll+0x7f152c]
V  [jvm.dll+0x3f5d4f]
V  [jvm.dll+0x3f7981]
C  [jli.dll+0x528f]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1257d]
C  [ntdll.dll+0x5aa48]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ffba98eef18, length=0, elements={
}

Java Threads: ( => current thread )

Other Threads:
  0x000001b3dcd53b10 GCTaskThread "GC Thread#0" [stack: 0x0000005a74d00000,0x0000005a74e00000] [id=35908]
  0x000001b3dcd64620 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000005a74e00000,0x0000005a74f00000] [id=2944]
  0x000001b3dcd65040 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000005a74f00000,0x0000005a75000000] [id=36620]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffba90a3937]

VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001b3dcce3130] Heap_lock - owner thread: 0x000001b3dcce6a30

Heap address: 0x0000000704400000, size: 4028 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
<Empty>

Heap:
 garbage-first heap   total 0K, used 0K [0x0000000704400000, 0x0000000800000000)
  region size 2048K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 0K, committed 0K, reserved 0K
  class space    used 0K, committed 0K, reserved 0K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)

Card table byte_map: [0x000001b3f04b0000,0x000001b3f0c90000] _byte_map_base: 0x000001b3ecc8e000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001b3dcd54130, (CMBitMap*) 0x000001b3dcd54170
 Prev Bits: [0x000001b3f1470000, 0x000001b3f5360000)
 Next Bits: [0x000001b3f5360000, 0x000001b3f9250000)

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.005 Loaded shared library D:\PyCharm 2023.2.5\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

VM Operations (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff77e750000 - 0x00007ff77e75a000 	D:\PyCharm 2023.2.5\jbr\bin\java.exe
0x00007ffc24df0000 - 0x00007ffc25007000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffc23a60000 - 0x00007ffc23b24000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffc225a0000 - 0x00007ffc22947000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffbcd320000 - 0x00007ffbcd344000 	C:\WINDOWS\System32\ghijt64win10.dll
0x00007ffc22b20000 - 0x00007ffc22bd2000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffc23ba0000 - 0x00007ffc23c47000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffc22be0000 - 0x00007ffc22c88000 	C:\WINDOWS\System32\sechost.dll
0x00007ffc22950000 - 0x00007ffc22978000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffc22fb0000 - 0x00007ffc230c5000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffc221d0000 - 0x00007ffc222e1000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffc04a40000 - 0x00007ffc04a57000 	D:\PyCharm 2023.2.5\jbr\bin\jli.dll
0x00007ffc0ed60000 - 0x00007ffc0ed7b000 	D:\PyCharm 2023.2.5\jbr\bin\VCRUNTIME140.dll
0x00007ffc23270000 - 0x00007ffc2341e000 	C:\WINDOWS\System32\USER32.dll
0x00007ffc222f0000 - 0x00007ffc22316000 	C:\WINDOWS\System32\win32u.dll
0x00007ffc24680000 - 0x00007ffc246a9000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffc22a00000 - 0x00007ffc22b19000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffc103e0000 - 0x00007ffc10673000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.3527_none_270e469b73872a76\COMCTL32.dll
0x00007ffc22500000 - 0x00007ffc2259a000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffc23a20000 - 0x00007ffc23a51000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffc1a1d0000 - 0x00007ffc1a1dc000 	D:\PyCharm 2023.2.5\jbr\bin\vcruntime140_1.dll
0x00007ffbc0840000 - 0x00007ffbc08cd000 	D:\PyCharm 2023.2.5\jbr\bin\msvcp140.dll
0x00007ffba8db0000 - 0x00007ffba9a33000 	D:\PyCharm 2023.2.5\jbr\bin\server\jvm.dll
0x00007ffc20f10000 - 0x00007ffc20f5d000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffc156f0000 - 0x00007ffc15724000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffc13a20000 - 0x00007ffc13a2a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffbfd550000 - 0x00007ffbfd559000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ffc239a0000 - 0x00007ffc23a11000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffc20ef0000 - 0x00007ffc20f03000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffc211a0000 - 0x00007ffc211b8000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffc19dd0000 - 0x00007ffc19dda000 	D:\PyCharm 2023.2.5\jbr\bin\jimage.dll
0x00007ffc1f4f0000 - 0x00007ffc1f722000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffc246b0000 - 0x00007ffc24a38000 	C:\WINDOWS\System32\combase.dll
0x00007ffc23190000 - 0x00007ffc23267000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffbcc730000 - 0x00007ffbcc762000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffc22980000 - 0x00007ffc229f9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffc06a90000 - 0x00007ffc06ab5000 	D:\PyCharm 2023.2.5\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\PyCharm 2023.2.5\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.3527_none_270e469b73872a76;D:\PyCharm 2023.2.5\jbr\bin\server

VM Arguments:
java_command: git4idea.editor.GitRebaseEditorApp E:/code-hub/opt.fcst.profits.model/.git/COMMIT_EDITMSG
java_class_path (initial): D:/PyCharm 2023.2.5/plugins/vcs-git/lib/git4idea-rt.jar;D:/PyCharm 2023.2.5/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 264241152                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4223664128                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4223664128                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=D:\Java\jdk1.8.0_291
PATH=D:\Git\mingw64\libexec\git-core;D:\Git\mingw64\libexec\git-core;D:\Git\mingw64\bin;D:\Git\usr\bin;C:\Users\<USER>\bin;D:\pythonProject\ai.framework-dev\new-env1\Scripts;C:\Windows\System32\HWAudioDriver;C:\Windows;C:\Windows\system32;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\HWAudioDriverLibs;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Windows\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps;D:\Git\cmd;D:\apache-maven-3.9.4\bin;D:\gradle-8.2.1\bin;D:\anaconda3\Scripts;D:\anaconda3;D:\BIND9.16.30;D:\nodejs;C:\ProgramData\chocolatey\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;D:\cursor\resources\app\bin;D:\xshell;D:\xftp;D:\Java\jdk1.8.0_291\bin;D:\LingmaIDE\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Pandoc;D:\CLion 2024.1.4\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;D:\cursor\resources\app\bin;C:\Users\<USER>\.lmstudio\bin
USERNAME=z00610297
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 154 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

OOME stack traces (most recent first):
Classloader memory used:


---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.3527)
OS uptime: 2 days 21:14 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 154 stepping 3 microcode 0x41e, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb, hv
Processor Information for processor 0
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 1
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 2
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 3
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 4
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 5
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 6
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 7
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 8
  Max Mhz: 2100, Current Mhz: 1500, Mhz Limit: 1491
Processor Information for processor 9
  Max Mhz: 2100, Current Mhz: 1500, Mhz Limit: 1491
Processor Information for processor 10
  Max Mhz: 2100, Current Mhz: 1500, Mhz Limit: 1491
Processor Information for processor 11
  Max Mhz: 2100, Current Mhz: 1500, Mhz Limit: 1491
Processor Information for processor 12
  Max Mhz: 2100, Current Mhz: 1500, Mhz Limit: 1491
Processor Information for processor 13
  Max Mhz: 2100, Current Mhz: 1500, Mhz Limit: 1491
Processor Information for processor 14
  Max Mhz: 2100, Current Mhz: 1500, Mhz Limit: 1491
Processor Information for processor 15
  Max Mhz: 2100, Current Mhz: 1500, Mhz Limit: 1491

Memory: 4k page, system-wide physical 16108M (1009M free)
TotalPageFile size 57419M (AvailPageFile size 54M)
current process WorkingSet (physical memory assigned to process): 11M, peak: 11M
current process commit charge ("private bytes"): 71M, peak: 323M

vm_info: OpenJDK 64-Bit Server VM (17.0.9+7-b1000.46) for windows-amd64 JRE (17.0.9+7-b1000.46), built on 2023-10-27 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
