import io
import json
import os
import time

from obs import DeleteObjectsRequest, Object, ObsClient


class HisObsBaseError(Exception):
    """
    Base exception class for HisObs errors.
    """

    def __init__(self, http_code: int, err_code: str = "", message: str = ""):
        self.http_code = http_code
        self.err_code = err_code
        self.message = message
        super().__init__(self.http_code, self.err_code, self.message)

    def to_dict(self):
        return {
            "error_type": self.__class__.__name__,
            "http_code": self.http_code,
            "error_code": self.err_code,
            "message": self.message
        }

    def __str__(self) -> str:
        return json.dumps(self.to_dict())


class HisObsRedirectError(HisObsBaseError):
    """
    Exception raised for redirect errors (3xx status codes).
    """

    def __init__(self, http_code: int = 300, err_code: str = "", message: str = ""):
        super().__init__(http_code, err_code, message)


class HisObsClientError(HisObsBaseError):
    """
    Exception raised for client errors (4xx status codes).
    """

    def __init__(self, http_code: int = 400, err_code: str = "", message: str = ""):
        super().__init__(http_code, err_code, message)


class HisObsServerError(HisObsBaseError):
    """
    Exception raised for server errors (5xx status codes).
    """

    def __init__(self, http_code: int = 500, err_code: str = "", message: str = ""):
        super().__init__(http_code, err_code, message)


def check_response_status(response):
    """
    Server response HTTP Code includes 2xx, 3xx, 4xx, 5xx
    Only 2xx stands for success
    """
    if 300 <= response.status < 400:
        raise HisObsRedirectError(response.status, response.errorCode, response.errorMessage)
    elif 400 <= response.status < 500:
        raise HisObsClientError(response.status, response.errorCode, response.errorMessage)
    elif 500 <= response.status:
        raise HisObsServerError(response.status, response.errorCode, response.errorMessage)


class HisObsClient:
    """
    OBS Client for HIS
    """

    def __init__(
        self,
        access_key: str,
        secret_key: str,
        bucket: str,
        endpoint: str,
        max_retry_count: int = 3,
        max_redirect_count: int = 10,
        timeout: int = 2,
        long_conn_mode: bool = True,
        path_style: bool = True,
        signature: str = "v2",
        is_signature_negotiation: bool = True,
        *args, **kwargs
    ):
        self._bucket = bucket
        self._session = ObsClient(
            access_key_id=access_key,
            secret_access_key=secret_key,
            server=endpoint,
            long_conn_mode=long_conn_mode,
            path_style=path_style,
            signature=signature,
            is_signature_negotiation=is_signature_negotiation,
            max_retry_count=max_retry_count,
            max_redirect_count=max_redirect_count,
            timeout=timeout,
            *args, **kwargs
        )
        self.session = self._session.bucketClient(bucketName=bucket)
        self.creation_date = time.time()

    def ping(self):
        try:
            response = self.session.headBucket()
            check_response_status(response)
            return True
        except Exception:
            return False

    def release(self):
        """
        Releases session.
        """
        self._session.close()

    def get_object_metadata(self, object_key: str):
        """
        Retrieves metadata for an object.
        Warning: only this api use different error key-words.
        """
        response = self.session.getObjectMetadata(objectKey=object_key)
        check_response_status(response)
        return {
            "etag": response.body.etag,
            "size": response.body.contentLength,
            "last_updated": response.body.lastModified
        }

    def list_objects(self, prefix: str, max_objects=None) -> list[str]:
        """
        List objects with given prefix.
        """
        response = self.session.listObjects(prefix=prefix, max_keys=max_objects)
        check_response_status(response)
        return [obj.key for obj in response.body.contents]

    def download_object_to_memory(self, object_key: str) -> io.BytesIO:
        """
        Downloads an object to memory.
        """
        response = self.session.getObject(objectKey=object_key, loadStreamInMemory=True)
        check_response_status(response)
        return io.BytesIO(response.body.buffer)

    def download_object_to_disk(self, object_key: str, local_path: str):
        """
        Downloads an object to disk.
        """
        response = self.session.getObject(objectKey=object_key, downloadPath=local_path)
        check_response_status(response)

    def upload_object_from_memory(self, object_key: str, data):
        """
        Uploads an object to OBS server from memory.
        """
        response = self.session.putContent(objectKey=object_key, content=data)
        check_response_status(response)

    def upload_object_from_disk(self, object_key: str, local_path: str):
        """
        Uploads an object to OBS server from disk.
        If local path is a directory, create a path, otherwise upload the file.
        """
        if os.path.isdir(local_path):
            response = self.session.putContent(objectKey=object_key + "/", content=None)
        else:
            response = self.session.putFile(objectKey=object_key, file_path=local_path)
        check_response_status(response)

    def delete_object(self, object_key: str):
        """
        Deletes an object.
        """
        response = self.session.deleteObject(objectKey=object_key)
        check_response_status(response)

    def delete_objects(self, object_keys: list[str]):
        """
        Batch delete objects.
        """
        response = self.session.deleteObjects(
            deleteObjectsRequest=DeleteObjectsRequest(quiet=False, objects=[Object(key=obj) for obj in object_keys])
        )
        check_response_status(response)

    def upload_folder(self, obs_folder: str, local_folder: str):
        """
        Uploads the content of a local folder to OBS server folder.
        """
        for root, dirs, files in os.walk(local_folder):
            for path in dirs:
                relative_path = os.path.relpath(os.path.join(root, path), local_folder)
                object_key = os.path.join(obs_folder, relative_path).replace("\\", "/") + "/"
                self.upload_object_from_memory(object_key=object_key, data=None)
            for file in files:
                local_file_path = os.path.join(root, file)
                relative_path = os.path.relpath(local_file_path, local_folder)
                object_key = os.path.join(obs_folder, relative_path).replace("\\", "/")
                self.upload_object_from_disk(object_key=object_key, local_path=local_file_path)

    def download_folder(self, obs_folder: str, local_folder: str, max_objects: int = None):
        """
        Downloads a folder from OBS server to local folder.
        """
        if not os.path.exists(local_folder):
            os.makedirs(local_folder)
        objects = self.list_objects(prefix=obs_folder, max_objects=max_objects)
        for obj in objects:
            if obj.endswith("/"):
                local_path = os.path.join(local_folder, obj).replace("\\", "/")
                os.mkdir(local_path)
            else:
                local_file = os.path.join(local_folder, obj).replace("\\", "/")
                self.download_object_to_disk(object_key=obj, local_path=local_file)

    def delete_folder(self, obs_folder: str, max_objects: int = None):
        """
        Deletes OBS server folder.
        """
        self.delete_objects(self.list_objects(prefix=obs_folder, max_objects=max_objects)[::-1])

    def get_object_download_url(self, object_key: str, expires=300) -> str:
        """
        Get a pre-signed download URL of an object
        """
        response = self._session.createSignedUrl("GET", self._bucket, object_key, expires=expires)
        return response.signedUrl

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_value, exc_traceback):
        self.release()
