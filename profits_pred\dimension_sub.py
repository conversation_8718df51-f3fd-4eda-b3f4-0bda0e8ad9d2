import os

import pandas as pd

from db.get_data import get_data
from db.save_to_db import save_to_db
from profits_sequence_pred import integrate_results
from datetime import datetime
from process_after_pred import after_process
from dimension_constant import dimension_subcategory_dim, check_input

# 预测因子
dimension_pred_lst = ['unit_cost', 'unit_price', 'carryover_rate']
nondimension_pred_lst = ['rev_percent', 'mgp_ratio']
# 区间预测
dim_need_interval_prediction = ['unit_cost', 'unit_price']
cols_to_check = dimension_pred_lst+nondimension_pred_lst


def profit_forecast(pred_version: int):
    with open(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'sql', 'dimension_sub.sql'), 'r+',
                  encoding='utf-8') as f:

        dimension_sub_sql = f.read()
        dimension_sub_sql = dimension_sub_sql.format(pred_version=pred_version)
    df = get_data(dimension_sub_sql)
    # 检查结果
    check_input(df, cols_to_check)

    # 量纲子类
    dimension_subcategory_his_df = df[(df['scenarios'] == '量纲子类') & (df['dimension_subcategory_code'] != 'NOSUB')]
    dimension_subcategory_pred_df = integrate_results(his_df=dimension_subcategory_his_df,
                                                      dim=dimension_subcategory_dim,pred_cols=dimension_pred_lst,
                                                      period_col='target_period', pred_version=pred_version)
    df = dimension_subcategory_pred_df.groupby(dimension_subcategory_dim + ['target_period']).agg(
        {i + '_fcst': 'max' for i in dimension_pred_lst} |
        {i + '_fcst_upper': 'max' for i in dim_need_interval_prediction} |
        {i + '_fcst_lower': 'max' for i in dim_need_interval_prediction} |
        {i + '_fcst_conf': 'max' for i in dim_need_interval_prediction}
    ).reset_index()
    df['period_id'] = pred_version
    # 异常后处理
    after_process(df, dimension_subcategory_his_df, dimension_subcategory_dim)
    # 存在未合并的数据，二次合并
    if df.isnull().any().any():
        df = df.groupby(dimension_subcategory_dim + ['target_period']).agg(
            {i + '_fcst': 'max' for i in dimension_pred_lst} |
            {i + '_fcst_upper': 'max' for i in dim_need_interval_prediction} |
            {i + '_fcst_lower': 'max' for i in dim_need_interval_prediction} |
            {i + '_fcst_conf': 'max' for i in dim_need_interval_prediction}).reset_index()

    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    df.to_csv(os.path.join(os.path.dirname(__file__), "results", f"dimension_subcategory_pred_df_{timestamp}_cja.csv"))
    save_to_db(df, pred_version, scenarios='DIM')



